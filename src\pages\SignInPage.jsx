import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';
import { LogIn, Mail, Lock } from 'lucide-react';

const SignInPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { signIn } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    const { error: signInError } = await signIn(email, password);
    setLoading(false);
    if (signInError) {
      setError(signInError.message || "Anmeldung fehlgeschlagen. Überprüfe E-Mail und Passwort.");
      toast({ title: "Anmeldefehler", description: signInError.message || "Überprüfe deine Eingaben.", variant: "destructive" });
    } else {
      toast({ title: "Erfolgreich angemeldet!", description: "Willkommen zurück!" });
      // Navigation erfolgt durch den AuthContext
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-900 via-primary-dark-green to-slate-800 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="w-full max-w-md bg-slate-800 text-slate-100 border-primary-gold shadow-xl">
          <CardHeader className="text-center">
            <img-replace src="/logo.png" alt="ColorConda Logo" className="w-24 h-24 mx-auto mb-6 rounded-full shadow-lg border-2 border-primary-gold p-1"/>
            <CardTitle className="text-3xl font-heading text-primary-gold">Willkommen zurück!</CardTitle>
            <CardDescription className="text-slate-400">Melde dich an, um ColorConda zu spielen.</CardDescription>
          </CardHeader>
          <CardContent>
            {error && <p className="text-red-500 text-sm bg-red-900/30 p-3 rounded-md mb-4 text-center">{error}</p>}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-300">E-Mail</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="email" 
                    type="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)} 
                    required 
                    className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-300">Passwort</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="password" 
                    type="password" 
                    value={password} 
                    onChange={(e) => setPassword(e.target.value)} 
                    required 
                    className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                    placeholder="••••••••"
                  />
                </div>
              </div>
              <div className="flex items-center justify-end">
                <Link to="/forgot-password" className="text-sm text-primary-gold hover:underline">Passwort vergessen?</Link>
              </div>
              <Button type="submit" className="w-full bg-primary-gold hover:bg-primary-gold/80 text-slate-900 font-semibold py-3 text-lg" disabled={loading}>
                <LogIn className="mr-2 h-5 w-5" /> {loading ? 'Melde an...' : 'Anmelden'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col items-center space-y-3 pt-6">
            <p className="text-sm text-slate-400">
              Noch kein Konto? <Link to="/signup" className="text-primary-gold hover:underline">Jetzt registrieren</Link>
            </p>
            <Link to="/" className="text-xs text-slate-500 hover:text-primary-gold transition-colors">
              Zurück zur Startseite
            </Link>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default SignInPage;