// Simple API endpoint for writing logs (if supported by hosting environment)
// This is a fallback that will likely not work in most static hosting environments

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { content, filename } = req.body;
    
    if (!content || !filename) {
      return res.status(400).json({ error: 'Missing content or filename' });
    }

    // In a real environment, you would write to a file system here
    // For now, just return success to prevent errors
    console.log(`[API] Would write log file: ${filename}`);
    console.log(`[API] Content length: ${content.length} characters`);
    
    return res.status(200).json({ 
      success: true, 
      message: 'Log writing not supported in this environment',
      filename 
    });
    
  } catch (error) {
    console.error('[API] Error in write-logs:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
