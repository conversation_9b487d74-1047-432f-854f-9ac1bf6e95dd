import { useCallback, useRef } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { makeAiMove } from '@/utils/aiPlayerLogic';
import { handleEndTurn } from '@/utils/gameLogic.jsx';
import { CARD_TYPES, SPECIAL_CARDS_NAMES } from '@/utils/gameRules';
import { debugLog, debugWarn, debugError } from '@/utils/debugUtils.jsx';
import { addCardToAiHistory, addBlockingToAiHistory, logAiHistoryDebug } from '@/utils/aiHistoryUtils.js';

export const useAiTurn = (gameState, setGameState, playSelectedCardLogic) => {
  const { toast } = useToast();
  const aiTurnInProgressRef = useRef(false);

  const executeAiTurn = useCallback(async (currentAiPlayerId) => {
    if (aiTurnInProgressRef.current) {
      debugWarn(`[AITurn][P${currentAiPlayerId}] AI turn already in progress. Skipping.`);
      return;
    }

    // Sicherheitscheck: Verhindere Endlosschleifen
    const maxTurnAttempts = 10;
    const turnAttemptKey = `ai_turn_${currentAiPlayerId}_${Date.now()}`;
    let turnAttempts = parseInt(sessionStorage.getItem(turnAttemptKey) || '0');

    if (turnAttempts >= maxTurnAttempts) {
      debugError(`[AITurn][P${currentAiPlayerId}] Maximum turn attempts (${maxTurnAttempts}) reached. Forcing turn end.`);
      setGameState(prev => {
        const newState = handleEndTurn(prev, toast, () => {}, currentAiPlayerId);
        return {...newState, toastInfoForDisplay: { title: `Gegner ${currentAiPlayerId} Zug beendet (Sicherheit)`, description: "Zug automatisch beendet", duration: 2000 }};
      });
      sessionStorage.removeItem(turnAttemptKey);
      return;
    }

    sessionStorage.setItem(turnAttemptKey, (turnAttempts + 1).toString());
    aiTurnInProgressRef.current = true;

    const aiPlayer = gameState.aiOpponentsData.find(opp => opp.id === currentAiPlayerId);
    debugLog(`[DEBUG][KI-Zug-Start] Gegner ${currentAiPlayerId}: isBlockedByGrube=${!!(aiPlayer && aiPlayer.isBlockedByGrube)}, playerTurnsWhileOpponentBlocked=${gameState.playerTurnsWhileOpponentBlocked}`);
    if (aiPlayer && aiPlayer.isBlockedByGrube) {
      // Prüfe, ob Farbschutz gespielt werden kann
      const farbenschutzCard = aiPlayer.hand.find(c => c.name === SPECIAL_CARDS_NAMES.COLOR_PROTECTION);
      if (farbenschutzCard) {
        debugLog(`[DEBUG] KI ist blockiert, spielt aber Farbschutz.`);
        playSelectedCardLogic(farbenschutzCard, null, null, false, null, currentAiPlayerId);
        setTimeout(() => {
          setGameState(prev => {
            const newState = handleEndTurn(prev, toast, () => {}, currentAiPlayerId);
            return {
              ...newState,
              toastInfoForDisplay: {
                title: `Gegner ${currentAiPlayerId} ist durch Schlangengrube blockiert, spielt Farbschutz und setzt aus.`,
                duration: 1800
              }
            };
          });
          aiTurnInProgressRef.current = false;
        }, 500);
        return;
      }
      debugLog(`[DEBUG] KI-Zug blockiert: isBlockedByGrube=${!!(aiPlayer && aiPlayer.isBlockedByGrube)}, playerTurnsWhileOpponentBlocked=${gameState.playerTurnsWhileOpponentBlocked}`);

      // Füge Blockierungs-Eintrag zur History hinzu
      setGameState(prev => {
        const newHistory = addBlockingToAiHistory(prev.aiPlayHistory, currentAiPlayerId, prev.turnNumber);
        logAiHistoryDebug(newHistory, `After blocking Player ${currentAiPlayerId}`);

        const newState = handleEndTurn(prev, toast, () => {}, currentAiPlayerId);
        return {
          ...newState,
          aiPlayHistory: newHistory,
          toastInfoForDisplay: {
            title: `Gegner ${currentAiPlayerId} ist durch Schlangengrube blockiert und setzt aus.`,
            duration: 1800
          }
        };
      });
      aiTurnInProgressRef.current = false;
      return;
    }

    setGameState(prev => ({...prev, toastInfoForDisplay: { title: `Gegner ${currentAiPlayerId} denkt nach...`, duration: 2000 }}));

    let aiPlayedAtLeastOneCard = false;
    let actionsTakenThisTurn = 0;
    const maxActionsPerAiTurn = 5; // Erhöht auf 5, um Verdoppler-Effekt zu ermöglichen
    const cardsPlayedThisTurn = []; // Sammle alle gespielten Karten für History

    for (let actionAttempt = 0; actionAttempt < maxActionsPerAiTurn; actionAttempt++) {
        // Erstelle aktuellen Snapshot für jede Aktion (wichtig für History-Updates)
        const currentGameStateSnapshotForAi = { ...gameState };
        const aiPlayer = currentGameStateSnapshotForAi.aiOpponentsData.find(opp => opp.id === currentAiPlayerId);
        if (!aiPlayer) {
            debugError(`[AITurn][P${currentAiPlayerId}] AI Player data not found!`);
            break;
        }

        // Prüfe ob ein Verdoppler in diesem Zug gespielt wurde (aus der History)
        const verdopplerPlayedThisTurn = currentGameStateSnapshotForAi.playedSpecialCardsHistory.filter(
            c => c.name === SPECIAL_CARDS_NAMES.DOUBLER &&
                 c.turnPlayed === currentGameStateSnapshotForAi.turnNumber &&
                 c.playerId === currentAiPlayerId
        );

        const isVerdopplerActive = verdopplerPlayedThisTurn.length > 0;

        // Verdoppler-Effekt: Erlaubt bis zu 3 Karten flexibel (Farbe/Spezial)
        const currentMaxTotalPlays = isVerdopplerActive ? 3 : 2;
        const currentMaxColorPlays = isVerdopplerActive ? 3 : 1;
        const currentMaxSpecialPlays = isVerdopplerActive ? 3 : 1;

        debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Verdoppler-Status: isActive=${isVerdopplerActive}, verdopplerCount=${verdopplerPlayedThisTurn.length}, maxTotal=${currentMaxTotalPlays}, maxColor=${currentMaxColorPlays}, maxSpecial=${currentMaxSpecialPlays}`);
        debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Played Special Cards History:`, currentGameStateSnapshotForAi.playedSpecialCardsHistory?.filter(c => c.playerId === currentAiPlayerId && c.turnPlayed === currentGameStateSnapshotForAi.turnNumber) || []);

        const currentColorsPlayed = aiPlayer.colorCardsPlayedThisTurn || 0;
        const currentSpecialsPlayed = aiPlayer.specialCardsPlayedThisTurn || 0;

        debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Current cards played: Colors=${currentColorsPlayed}, Specials=${currentSpecialsPlayed}, Total=${currentColorsPlayed + currentSpecialsPlayed}`);

        // Aktualisiere die Zähler mit den neuesten Werten aus dem GameState
        const latestAiPlayer = currentGameStateSnapshotForAi.aiOpponentsData.find(opp => opp.id === currentAiPlayerId);
        const latestColorsPlayed = latestAiPlayer?.colorCardsPlayedThisTurn || 0;
        const latestSpecialsPlayed = latestAiPlayer?.specialCardsPlayedThisTurn || 0;

        // KORREKTUR: Strikte Limits - max 1 Farbe + 1 Sonder pro Zug (auch mit Verdoppler)
        if (latestColorsPlayed >= 1 && latestSpecialsPlayed >= 1) {
            debugLog(`[AITurn][P${currentAiPlayerId}] ✅ LIMIT ERREICHT: 1 Farbe + 1 Sonder gespielt (${latestColorsPlayed}/${latestSpecialsPlayed}) - Zug beenden`);
            break;
        }

        if (latestColorsPlayed + latestSpecialsPlayed >= currentMaxTotalPlays) {
            debugLog(`[AITurn][P${currentAiPlayerId}] Max total plays for turn reached (${latestColorsPlayed + latestSpecialsPlayed}/${currentMaxTotalPlays}).`);
            break;
        }
         if (aiPlayer.mustPlaySpecialCard && latestSpecialsPlayed === 0 && latestColorsPlayed > 0) {
             debugLog(`[AITurn][P${currentAiPlayerId}] Must play special card, but already played color.`);
             break;
         }

        if (actionAttempt > 0) await new Promise(resolve => setTimeout(resolve, 1200));

        const moveDecision = await makeAiMove(aiPlayer, currentGameStateSnapshotForAi);

        if (moveDecision.error) {
            debugError(`[AITurn][P${currentAiPlayerId}] AI move decision error:`, moveDecision.error);
            setGameState(prev => ({...prev, toastInfoForDisplay: { title: `KI Fehler ${currentAiPlayerId}`, description: `Fehler bei Zugentscheidung: ${moveDecision.error || 'Unbekannt'}`, variant: "destructive" }}));
        }

        const { cardToPlay, targetSnakeIndex, position } = moveDecision;

        if (cardToPlay && cardToPlay.id) {
            const cardInHand = aiPlayer.hand.find(c => c.id === cardToPlay.id);
            if (!cardInHand) {
                 debugError(`[AITurn][P${currentAiPlayerId}] AI tried to play card ${cardToPlay.id} not in hand. Aborting this action.`);
                 continue;
            }

            // KORREKTUR: Strikte Prüfung der Kartenlimits
            if (cardInHand.type === CARD_TYPES.COLOR && latestColorsPlayed >= 1) {
                debugLog(`[AITurn][P${currentAiPlayerId}] ❌ AI tried to play color card but limit reached (${latestColorsPlayed}/1 max).`);
                continue;
            }
            if (cardInHand.type === CARD_TYPES.SPECIAL && latestSpecialsPlayed >= 1) {
                debugLog(`[AITurn][P${currentAiPlayerId}] ❌ AI tried to play special card but limit reached (${latestSpecialsPlayed}/1 max).`);
                continue;
            }

            // SPEZIALFALL: Regenbogenschlange ist Sonderkarte, nicht Farbkarte
            if (cardInHand.name === 'Regenbogenschlange' && latestSpecialsPlayed >= 1) {
                debugLog(`[AITurn][P${currentAiPlayerId}] ❌ AI tried to play Regenbogenschlange but special limit reached (${latestSpecialsPlayed}/1 max).`);
                continue;
            }
            if (aiPlayer.isBlockedByGrube && cardInHand.type === CARD_TYPES.COLOR) {
                debugLog(`[AITurn][P${currentAiPlayerId}] AI is blocked by grube and tried to play color card.`);
                continue;
            }

            // Debug: Zeige welche Karte gespielt wird
            debugLog(`[DEBUG][KI-Spielt] Gegner ${currentAiPlayerId} spielt: ${cardInHand.name} (${cardInHand.type}, ${cardInHand.color || 'keine Farbe'})`);

            playSelectedCardLogic(cardInHand, 'Schlangenzone', position, false, targetSnakeIndex, currentAiPlayerId);
            aiPlayedAtLeastOneCard = true;
            actionsTakenThisTurn++;

            // Sammle Karte für History (wird am Ende des Zugs hinzugefügt)
            cardsPlayedThisTurn.push({
                name: cardInHand.name,
                type: cardInHand.type,
                color: cardInHand.color,
                timestamp: new Date().toISOString()
            });

            // Verbesserte Toast-Nachricht mit Kartendetails
            setGameState(prev => ({
                ...prev,
                toastInfoForDisplay: {
                    title: `Gegner ${currentAiPlayerId} spielt: ${cardInHand.name}`,
                    description: `${cardInHand.type === CARD_TYPES.COLOR ? 'Farbkarte' : 'Sonderkarte'} - Aktion ${actionsTakenThisTurn}/${currentMaxTotalPlays}`,
                    duration: 2500
                }
            }));

            // Kurze Pause für Toast-Anzeige und State-Update
            await new Promise(resolve => setTimeout(resolve, 500));

            // WICHTIG: Aktualisiere den Snapshot mit dem aktuellen State
            // Das ist kritisch für die Verdoppler-Erkennung!
            await new Promise(resolve => {
                setGameState(prev => {
                    currentGameStateSnapshotForAi = { ...prev };
                    debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] GameState-Snapshot aktualisiert nach Kartenspielen`);
                    debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] History nach Update:`, prev.playedSpecialCardsHistory?.filter(c => c.playerId === currentAiPlayerId && c.turnPlayed === prev.turnNumber) || []);
                    resolve();
                    return prev; // Keine Änderungen, nur Snapshot-Update
                });
            });

            const updatedAiPlayerAfterPlay = currentGameStateSnapshotForAi.aiOpponentsData.find(opp => opp.id === currentAiPlayerId);
            if (!updatedAiPlayerAfterPlay) {
                debugError(`[AITurn][P${currentAiPlayerId}] Aktualisierte KI-Spielerdaten nach dem Spielen nicht gefunden! Abbruch dieser Aktion.`);
                continue; // Springe zur nächsten Aktionsmöglichkeit
            }

            // Debug: Zeige aktualisierte Zähler und History
            debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] 🎴 KARTE GESPIELT: ${cardInHand.name} (${cardInHand.type})`);
            debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] 📊 Nach Kartenspielen: Colors=${updatedAiPlayerAfterPlay.colorCardsPlayedThisTurn || 0}/1, Specials=${updatedAiPlayerAfterPlay.specialCardsPlayedThisTurn || 0}/1`);

            // Debug: Prüfe aktuellen Verdoppler-Status nach dem Kartenspielen
            const verdopplerInHistoryAfterPlay = currentGameStateSnapshotForAi.playedSpecialCardsHistory.filter(
                c => c.name === SPECIAL_CARDS_NAMES.DOUBLER && c.turnPlayed === currentGameStateSnapshotForAi.turnNumber && c.playerId === currentAiPlayerId
            );
            const isVerdopplerJustPlayed = cardInHand.name === SPECIAL_CARDS_NAMES.DOUBLER;
            const isVerdopplerNowActive = verdopplerInHistoryAfterPlay.length > 0;

            // WICHTIG: Prüfe ob Limits erreicht sind (auch mit Verdoppler)
            const finalColorsPlayed = updatedAiPlayerAfterPlay.colorCardsPlayedThisTurn || 0;
            const finalSpecialsPlayed = updatedAiPlayerAfterPlay.specialCardsPlayedThisTurn || 0;

            if (finalColorsPlayed >= 1 && finalSpecialsPlayed >= 1) {
                debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] ✅ BEIDE LIMITS ERREICHT: 1 Farbe + 1 Sonder gespielt - ZUG MUSS BEENDET WERDEN`);
            }

            debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Nach Kartenspielen - Verdoppler: justPlayed=${isVerdopplerJustPlayed}, nowActive=${isVerdopplerNowActive}, historyCount=${verdopplerInHistoryAfterPlay.length}`);
            debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Verdoppler History nach Spielen:`, verdopplerInHistoryAfterPlay.map(v => `${v.name} (Turn: ${v.turnPlayed})`));
            debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Current cards played: Colors=${updatedAiPlayerAfterPlay.colorCardsPlayedThisTurn || 0}, Specials=${updatedAiPlayerAfterPlay.specialCardsPlayedThisTurn || 0}, Total=${(updatedAiPlayerAfterPlay.colorCardsPlayedThisTurn || 0) + (updatedAiPlayerAfterPlay.specialCardsPlayedThisTurn || 0)}`);

            if (updatedAiPlayerAfterPlay.mustPlaySpecialCard && (updatedAiPlayerAfterPlay.specialCardsPlayedThisTurn || 0) === 0) {
                 debugLog(`[AITurn][P${currentAiPlayerId}] AI still needs to play a special card.`);
            }

            // WICHTIG: Wenn ein Verdoppler gespielt wurde, sollte die KI versuchen, mehr Karten zu spielen
            if (isVerdopplerJustPlayed) {
                debugLog(`[DEBUG][AITurn][P${currentAiPlayerId}] Verdoppler gespielt! KI sollte jetzt bis zu 3 Karten total spielen können.`);
                // Setze die Schleife fort, um weitere Karten zu spielen
            }
        } else {
            debugLog(`[AITurn][P${currentAiPlayerId}] AI has no more suitable moves this action attempt or chooses to pass action. Reasoning: ${moveDecision.reasoning || 'N/A'}`);
            if (actionsTakenThisTurn === 0) aiPlayedAtLeastOneCard = false;
            break;
        }
    }

    if (!aiPlayedAtLeastOneCard && (currentGameStateSnapshotForAi.aiOpponentsData.find(opp => opp.id === currentAiPlayerId)?.hand.length || 0) > 0) {
      setGameState(prev => ({...prev, toastInfoForDisplay: { title: `Gegner ${currentAiPlayerId} passt.`, duration: 1500 }}));
      await new Promise(resolve => setTimeout(resolve, 500));
    } else if (aiPlayedAtLeastOneCard) {
      // Debug-Zusammenfassung des KI-Zugs
      const finalAiPlayer = currentGameStateSnapshotForAi.aiOpponentsData.find(opp => opp.id === currentAiPlayerId);
      const colorCount = finalAiPlayer?.colorCardsPlayedThisTurn || 0;
      const specialCount = finalAiPlayer?.specialCardsPlayedThisTurn || 0;
      debugLog(`[DEBUG][KI-Zug-Ende] Gegner ${currentAiPlayerId}: ${colorCount} Farbe(n), ${specialCount} Spezial(karten), Total: ${colorCount + specialCount}`);

      setGameState(prev => ({
        ...prev,
        toastInfoForDisplay: {
          title: `Gegner ${currentAiPlayerId} Zug beendet`,
          description: `${actionsTakenThisTurn} Aktion(en): ${colorCount} Farbe, ${specialCount} Spezial`,
          duration: 2000
        }
      }));
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    // Füge alle gespielten Karten zur History hinzu (am Ende des Zugs)
    if (cardsPlayedThisTurn.length > 0) {
      setGameState(prev => {
        let newHistory = [...prev.aiPlayHistory];

        // Erstelle einen einzigen History-Eintrag für alle Karten dieses Zugs
        const existingEntryIndex = newHistory.findIndex(
          h => h.playerId === currentAiPlayerId && h.turnNumber === prev.turnNumber
        );

        if (existingEntryIndex >= 0) {
          // Merge mit existierendem Eintrag
          newHistory[existingEntryIndex].cards = [...newHistory[existingEntryIndex].cards, ...cardsPlayedThisTurn];
        } else {
          // Neuer Eintrag
          newHistory.push({
            playerId: currentAiPlayerId,
            turnNumber: prev.turnNumber,
            cards: cardsPlayedThisTurn,
            blocked: false,
            timestamp: new Date().toISOString()
          });
        }

        debugLog(`[AI-History] Added ${cardsPlayedThisTurn.length} cards for Player ${currentAiPlayerId}, Turn ${prev.turnNumber}:`, cardsPlayedThisTurn.map(c => c.name));
        logAiHistoryDebug(newHistory, `After complete turn by Player ${currentAiPlayerId}`);

        return { ...prev, aiPlayHistory: newHistory };
      });
    }

    setGameState(prev => {
        const newState = handleEndTurn(prev, toast, () => {}, currentAiPlayerId);
        aiTurnInProgressRef.current = false;

        // Cleanup: Entferne Turn-Attempt-Tracking
        sessionStorage.removeItem(turnAttemptKey);

        return {...newState, toastInfoForDisplay: newState.toastInfo};
    });

  }, [gameState, setGameState, playSelectedCardLogic, toast]);

  return { executeAiTurn, aiTurnInProgressRef };
};
