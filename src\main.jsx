import React from 'react';
import ReactDOM from 'react-dom/client';
import App from '@/App';
import '@/index.css';
import { BrowserRouter } from 'react-router-dom';
import ErrorBoundary from '@/components/ErrorBoundary';
// AuthProvider wird jetzt in App.jsx verwendet, um die Routes zu umschließen

console.log("OpenRouter API Key (aus .env):", import.meta.env.VITE_OPENROUTER_API_KEY ? "Vorhanden" : "Nicht vorhanden oder leer");

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ErrorBoundary>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </ErrorBoundary>
  </React.StrictMode>
);
