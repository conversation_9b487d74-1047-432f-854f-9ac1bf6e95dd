import React from 'react';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Trophy, Award } from 'lucide-react';

const GameOverModal = ({ isOpen, scores, onPlayAgain }) => {
  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="bg-slate-800 text-slate-100 border-primary-gold max-w-md">
        <DialogHeader>
          <DialogTitle className="text-primary-gold font-heading text-2xl flex items-center">
            <Trophy className="h-8 w-8 mr-3 text-yellow-400" />
            Spielende!
          </DialogTitle>
          <DialogDescription className="text-slate-300">
            Das Spiel ist beendet. Hier sind die Ergebnisse:
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="my-4 max-h-[60vh] p-1">
          <div className="space-y-3">
            {scores.map((player, index) => (
              <div 
                key={player.playerId} 
                className={`p-3 rounded-lg border flex items-center justify-between
                  ${index === 0 ? 'bg-primary-gold/20 border-primary-gold shadow-lg' : 'bg-slate-700/70 border-slate-600'}`}
              >
                <div className="flex items-center">
                  {index === 0 && <Award className="h-6 w-6 mr-2 text-yellow-400" />}
                  <span className={`font-semibold ${index === 0 ? 'text-yellow-200 text-lg' : 'text-slate-200'}`}>
                    {index + 1}. {player.name}
                  </span>
                </div>
                <span className={`font-bold text-xl ${index === 0 ? 'text-yellow-300' : 'text-slate-100'}`}>
                  {player.score} Punkte
                </span>
              </div>
            ))}
          </div>
        </ScrollArea>

        <DialogFooter className="mt-6">
          <Button 
            onClick={onPlayAgain} 
            className="w-full bg-primary-burgundy hover:bg-primary-burgundy/80 text-white font-semibold py-3 text-lg"
          >
            Nochmal spielen
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default GameOverModal;