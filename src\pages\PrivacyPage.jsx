import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, ShieldCheck } from 'lucide-react';
import { motion } from 'framer-motion';

const PrivacyPage = () => {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-700 text-slate-200 p-6 sm:p-8 md:p-12"
    >
      <div className="max-w-3xl mx-auto">
        <motion.div 
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="mb-8"
        >
          <Button asChild variant="outline" className="text-purple-300 border-purple-500 hover:bg-purple-500/30 hover:text-white">
            <Link to="/">
              <ArrowLeft className="mr-2 h-5 w-5" />
              <PERSON><PERSON><PERSON> zum Hauptmenü
            </Link>
          </Button>
        </motion.div>

        <motion.div 
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="bg-slate-800/70 backdrop-blur-md p-6 sm:p-8 rounded-xl shadow-2xl"
        >
          <div className="flex items-center mb-6">
            <ShieldCheck className="w-10 h-10 sm:w-12 sm:h-12 text-green-400 mr-4" />
            <h1 className="text-3xl sm:text-4xl font-bold text-green-300">Datenschutzerklärung</h1>
          </div>

          <p className="mb-4 text-slate-300">
            Version: {new Date().toLocaleDateString()}
          </p>
          <p className="mb-4 text-slate-300">
            Wir nehmen den Schutz Ihrer persönlichen Daten sehr ernst. Diese Datenschutzerklärung informiert Sie darüber, wie wir mit Ihren Daten umgehen, wenn Sie unser Spiel "Card Snake" nutzen.
          </p>

          <h2 className="text-2xl font-semibold text-purple-300 mt-6 mb-3">1. Verantwortliche Stelle</h2>
          <p className="mb-4 text-slate-300">
            [Platzhalter: Name und Kontaktdaten des Verantwortlichen, z.B. Max Mustermann, Musterstraße 1, 12345 Musterstadt, E-Mail: <EMAIL>]
          </p>

          <h2 className="text-2xl font-semibold text-purple-300 mt-6 mb-3">2. Erhebung und Verarbeitung von Daten</h2>
          <p className="mb-2 text-slate-300">
            Card Snake ist als reine Client-Side-Anwendung konzipiert. Das bedeutet:
          </p>
          <ul className="list-disc list-inside mb-4 text-slate-300 pl-4 space-y-1">
            <li>Es werden keine Benutzerkonten erstellt oder persönliche Registrierungsdaten (wie Name, E-Mail-Adresse) serverseitig gespeichert.</li>
            <li>Spieldaten wie Highscores, erreichte Punktzahlen oder Spieleinstellungen (z.B. Anzahl der KI-Gegner, gewähltes KI-Modell) werden ausschließlich lokal in Ihrem Browser im LocalStorage gespeichert. Diese Daten verlassen Ihren Computer nicht und werden nicht an uns oder Dritte übertragen.</li>
            <li>Für die Funktion der KI-Gegner wird eine Verbindung zum OpenRouter API hergestellt. Dabei wird Ihr VITE_OPENROUTER_API_KEY (sofern von Ihnen konfiguriert) verwendet. Es werden nur die für den Spielzug notwendigen, anonymisierten Spieldaten (aktueller Spielzustand) an die API gesendet. OpenRouter unterliegt eigenen Datenschutzbestimmungen, die Sie bitte dort einsehen. Wir speichern keine Antworten der KI-Modelle dauerhaft.</li>
          </ul>

          <h2 className="text-2xl font-semibold text-purple-300 mt-6 mb-3">3. LocalStorage</h2>
          <p className="mb-4 text-slate-300">
            Wie erwähnt, nutzt Card Snake den LocalStorage Ihres Browsers, um Spielstände und Einstellungen zu speichern. Sie können diese Daten jederzeit selbst löschen, indem Sie die Browserdaten für diese Webseite entfernen (üblicherweise in den Browser-Einstellungen unter "Datenschutz" oder "Cookies und Website-Daten").
          </p>
          
          <h2 className="text-2xl font-semibold text-purple-300 mt-6 mb-3">4. Cookies</h2>
          <p className="mb-4 text-slate-300">
            Card Snake verwendet keine Cookies für Tracking- oder Analysezwecke. Notwendige technische Cookies könnten von eingebundenen Diensten (z.B. für die Bereitstellung der Webanwendung selbst) verwendet werden, dienen aber nicht der persönlichen Identifizierung für Werbezwecke durch uns.
          </p>

          <h2 className="text-2xl font-semibold text-purple-300 mt-6 mb-3">5. Ihre Rechte</h2>
          <p className="mb-4 text-slate-300">
            Da wir keine personenbezogenen Daten über die lokale Speicherung hinaus aktiv erheben oder serverseitig verarbeiten, sind Ihre Rechte auf Auskunft, Berichtigung, Löschung etc. primär durch Ihre Kontrolle über die lokalen Browserdaten abgedeckt. Sollten Sie Fragen zum Datenschutz haben, kontaktieren Sie uns bitte unter der oben genannten Adresse.
          </p>

          <h2 className="text-2xl font-semibold text-purple-300 mt-6 mb-3">6. Änderungen dieser Datenschutzerklärung</h2>
          <p className="text-slate-300">
            Wir behalten uns vor, diese Datenschutzerklärung anzupassen, damit sie stets den aktuellen rechtlichen Anforderungen entspricht oder um Änderungen unserer Leistungen in der Datenschutzerklärung umzusetzen. Für Ihren erneuten Besuch gilt dann die neue Datenschutzerklärung.
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default PrivacyPage;