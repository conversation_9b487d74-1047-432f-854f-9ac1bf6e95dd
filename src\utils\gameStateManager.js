import { createDeck, shuffleDeck, dealCards, createTasks, createPlayerAuxCards, CARD_TYPES } from '@/utils/gameRules';

export const initializeGameState = (numPlayers = 2) => {
  const { openTasks, secretTasks } = createTasks();
  const shuffledOpenTasks = shuffleDeck([...openTasks]);
  const shuffledSecretTasks = shuffleDeck([...secretTasks]);

  return {
    deck: [],
    playerHand: [],
    playerSnakes: [[]], 
    activeSnakeIndex: 0,
    aiOpponentsData: [],
    discardPile: [],
    openTasks: shuffledOpenTasks.slice(0, 3),
    availableOpenTaskCards: shuffledOpenTasks.slice(3),
    secretTask: shuffledSecretTasks.pop(),
    playerTasks: [],
    auxCards: createPlayerAuxCards(),
    points: 0,
    moves: 0,
    playerActualTurns: 0,
    timeElapsed: '00:00',
    gamePhase: 'setup', 
    currentPlayerId: 0, 
    isPlayerTurn: true,
    selectedCardForPlay: null,
    toastInfo: null,
    toastInfoForDisplay: null,
    modalRequired: null,
    turnNumber: 1,
    colorCardsPlayedThisTurn: 0,
    specialCardsPlayedThisTurn: 0,
    mustPlaySpecialCard: false,
    playerProtected: false,
    schlangenkorbPlayState: { active: false, cardsToPlay: 0, playedCount: 0 },
    comebackActive: false,
    comebackActionsRemaining: 0,
    comebackActionsRemainingInitial: 0,
    playedSpecialCardsHistory: [],
    lastMoltingResult: null,
    playerFinalScores: [],
    numPlayers: numPlayers,
  };
};

export const startTimer = (timerIntervalRef, setGameState) => {
  let seconds = 0;
  timerIntervalRef.current = setInterval(() => {
    seconds++;
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    setGameState(prev => ({ ...prev, timeElapsed: `${mins}:${secs}` }));
  }, 1000);
};

export const stopTimer = (timerIntervalRef) => {
  if (timerIntervalRef.current) {
    clearInterval(timerIntervalRef.current);
  }
};