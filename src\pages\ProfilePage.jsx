import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';
import { User, Mail, Save, Shield, LogOut } from 'lucide-react';
import Layout from '@/components/shared/Layout';

const ProfilePage = () => {
  const { user, updateProfile, changePassword, signOut } = useAuth();
  const { toast } = useToast();

  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  // const [currentPassword, setCurrentPassword] = useState(''); // For password change, Supabase doesn't require current if user is authenticated
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');

  const [loadingProfile, setLoadingProfile] = useState(false);
  const [loadingPassword, setLoadingPassword] = useState(false);
  const [profileError, setProfileError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  useEffect(() => {
    if (user) {
      setUsername(user.user_metadata?.username || user.email?.split('@')[0] || '');
      setEmail(user.email || '');
    }
  }, [user]);

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setProfileError('');
    setLoadingProfile(true);
    const { error } = await updateProfile({ username });
    setLoadingProfile(false);
    if (error) {
      setProfileError(error.message || "Fehler beim Aktualisieren des Profils.");
      toast({ title: "Profilfehler", description: error.message, variant: "destructive" });
    } else {
      toast({ title: "Profil aktualisiert", description: "Deine Profilinformationen wurden gespeichert." });
    }
  };

  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setPasswordError('');
    if (newPassword !== confirmNewPassword) {
      setPasswordError("Die neuen Passwörter stimmen nicht überein.");
      toast({ title: "Passwortfehler", description: "Die neuen Passwörter stimmen nicht überein.", variant: "destructive" });
      return;
    }
    if (newPassword.length < 6) {
      setPasswordError("Das neue Passwort muss mindestens 6 Zeichen lang sein.");
      toast({ title: "Passwortfehler", description: "Das neue Passwort muss mindestens 6 Zeichen lang sein.", variant: "destructive" });
      return;
    }
    setLoadingPassword(true);
    const { error } = await changePassword(newPassword); // No currentPassword needed for Supabase's updateUser for password
    setLoadingPassword(false);
    if (error) {
      setPasswordError(error.message || "Fehler beim Ändern des Passworts.");
      toast({ title: "Passwortfehler", description: error.message, variant: "destructive" });
    } else {
      toast({ title: "Passwort geändert", description: "Dein Passwort wurde erfolgreich geändert." });
      //setCurrentPassword('');
      setNewPassword('');
      setConfirmNewPassword('');
    }
  };

  if (!user) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-full text-slate-100">
          <p>Lade Benutzerdaten...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <motion.div 
        className="container mx-auto px-4 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="max-w-2xl mx-auto bg-slate-800 text-slate-100 border-primary-gold shadow-xl">
          <CardHeader>
            <CardTitle className="text-3xl font-heading text-primary-gold flex items-center">
              <User className="mr-3 h-8 w-8" /> Dein Profil
            </CardTitle>
            <CardDescription className="text-slate-400">
              Verwalte deine Kontoinformationen und Sicherheitseinstellungen.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            {/* Profilinformationen */}
            <form onSubmit={handleProfileUpdate} className="space-y-6">
              <h3 className="text-xl font-semibold text-yellow-300 border-b border-slate-700 pb-2">Profilinformationen</h3>
              {profileError && <p className="text-red-400 bg-red-900/30 p-2 rounded text-sm">{profileError}</p>}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-slate-300">Benutzername</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="username" 
                    type="text" 
                    value={username} 
                    onChange={(e) => setUsername(e.target.value)} 
                    className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-300">E-Mail (kann nicht geändert werden)</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="email" 
                    type="email" 
                    value={email} 
                    readOnly 
                    className="pl-10 bg-slate-700/50 border-slate-600 text-slate-400 cursor-not-allowed"
                  />
                </div>
              </div>
              <Button type="submit" className="w-full sm:w-auto bg-primary-gold hover:bg-primary-gold/80 text-slate-900 font-semibold" disabled={loadingProfile}>
                <Save className="mr-2 h-4 w-4" /> {loadingProfile ? 'Speichere...' : 'Profil speichern'}
              </Button>
            </form>

            {/* Passwort ändern */}
            <form onSubmit={handlePasswordChange} className="space-y-6">
              <h3 className="text-xl font-semibold text-yellow-300 border-b border-slate-700 pb-2">Passwort ändern</h3>
              {passwordError && <p className="text-red-400 bg-red-900/30 p-2 rounded text-sm">{passwordError}</p>}
              {/*
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Aktuelles Passwort</Label>
                <Input id="currentPassword" type="password" value={currentPassword} onChange={(e) => setCurrentPassword(e.target.value)} required />
              </div>
              */}
              <div className="space-y-2">
                <Label htmlFor="newPassword" className="text-slate-300">Neues Passwort</Label>
                 <Input 
                    id="newPassword" 
                    type="password" 
                    value={newPassword} 
                    onChange={(e) => setNewPassword(e.target.value)} 
                    required 
                    className="bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100"
                    placeholder="Mindestens 6 Zeichen"
                  />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmNewPassword" className="text-slate-300">Neues Passwort bestätigen</Label>
                <Input 
                    id="confirmNewPassword" 
                    type="password" 
                    value={confirmNewPassword} 
                    onChange={(e) => setConfirmNewPassword(e.target.value)} 
                    required 
                    className="bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100"
                    placeholder="Neues Passwort wiederholen"
                  />
              </div>
              <Button type="submit" className="w-full sm:w-auto bg-primary-burgundy hover:bg-primary-burgundy/80 text-white font-semibold" disabled={loadingPassword}>
                <Shield className="mr-2 h-4 w-4" /> {loadingPassword ? 'Ändere...' : 'Passwort ändern'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-end pt-6 border-t border-slate-700">
             <Button variant="ghost" onClick={signOut} className="text-red-400 hover:bg-red-900/30 hover:text-red-300">
              <LogOut className="mr-2 h-4 w-4" /> Abmelden
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </Layout>
  );
};

export default ProfilePage;