import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Zap, Clock, Target, Move, CheckCircle, Shield, Trophy } from 'lucide-react';
import { motion } from 'framer-motion';
import { CARD_TYPES, SPECIAL_CARDS_NAMES } from '@/utils/gameRules';
import { isDebugMode } from '@/utils/debugUtils.jsx';

const StatItem = ({ icon: Icon, label, value, colorClass, delay, small = false }) => (
  <motion.div 
    className={`${small ? 'p-1' : 'p-2'} bg-slate-600/60 rounded-lg shadow-md`}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4, delay: delay }}
  >
    <Icon className={`${small ? 'h-4 w-4' : 'h-5 w-5'} mx-auto ${colorClass} mb-1`} />
    <p className={`${small ? 'text-xs' : 'text-sm'} text-slate-300`}>{label}</p>
    <p className={`${small ? 'text-sm' : 'text-lg'} font-semibold ${colorClass}`}>{value}</p>
  </motion.div>
);

// Hilfsfunktion um Farbgruppen-Punkte korrekt nach Schlangentanz-Regeln zu berechnen
const calculateColorGroupPoints = (snakes) => {
  let points = 0;
  snakes.forEach(snake => {
    if (!snake || snake.length < 3) return; // Mindestens 3 Karten für eine gültige Schlange
    for (let i = 0; i < snake.length; i++) {
      if (snake[i].type === CARD_TYPES.COLOR) {
        const color = snake[i].color;
        let currentLength = 1;
        let groupPoints = snake[i].points;
        for (let j = i + 1; j < snake.length; j++) {
          if (snake[j].type === CARD_TYPES.COLOR && snake[j].color === color) {
            currentLength++;
            groupPoints += snake[j].points;
          } else {
            break;
          }
        }
        if (currentLength === 3) points += groupPoints; 
        else if (currentLength === 4) points += groupPoints * 2; 
        else if (currentLength >= 5) points += groupPoints * 3; 
        i += currentLength - 1; 
      }
    }
  });
  return points;
};

// Hilfsfunktion um Diversitäts-Bonus zu berechnen
const calculateDiversityBonus = (snakes) => {
  let totalBonus = 0;
  snakes.forEach(snake => {
    if (!snake || snake.length === 0) return;
    const colorsInSnake = new Set();
    snake.forEach(card => {
      if (card.type === CARD_TYPES.COLOR) {
        colorsInSnake.add(card.color);
      }
      // Regenbogenschlange zählt als zusätzliche Farbe
      if (card.name === 'Regenbogenschlange') {
        if (colorsInSnake.size < 5) {
          colorsInSnake.add(`Regenbogen-${Math.random()}`); 
        }
      }
    });
    const distinctColors = colorsInSnake.size;
    if (distinctColors === 2) totalBonus += 1;
    else if (distinctColors === 3) totalBonus += 2;
    else if (distinctColors === 4) totalBonus += 3;
    else if (distinctColors >= 5) totalBonus += 4;
  });
  return totalBonus;
};

// Hilfsfunktion um Punkte korrekt nach Schlangentanz-Regeln zu berechnen
const calculatePlayerScore = (gameState) => {
  let totalScore = 0;
  
  // Punkte aus Farbgruppen (mindestens 3 aufeinanderfolgende gleiche Farben)
  totalScore += calculateColorGroupPoints(gameState.playerSnakes);
  
  // Diversitäts-Bonus für verschiedene Farben in einer Schlange
  totalScore += calculateDiversityBonus(gameState.playerSnakes);
  
  // Punkte aus gelösten Aufgaben
  gameState.playerTasks.forEach(task => {
    totalScore += task.points || 0;
  });
  
  return totalScore;
};

// Hilfsfunktion um die längste Schlange zu finden
const findLongestSnake = (gameState) => {
  let longestLength = 0;
  let longestOwner = '';
  
  // Prüfe Spieler-Schlangen
  gameState.playerSnakes.forEach((snake, index) => {
    if (snake.length > longestLength) {
      longestLength = snake.length;
      longestOwner = 'Du';
    }
  });
  
  // Prüfe KI-Gegner-Schlangen
  gameState.aiOpponentsData.forEach((opponent) => {
    opponent.snakes.forEach(snake => {
      if (snake.length > longestLength) {
        longestLength = snake.length;
        longestOwner = opponent.name;
      }
    });
  });
  
  return { length: longestLength, owner: longestOwner };
};

// Hilfsfunktion um echte Spielzüge zu berechnen
const calculateActualTurns = (gameState) => {
  // Verwende die neue playerActualTurns Eigenschaft
  return gameState.playerActualTurns || 0;
};

// Berechne Punkte aus gelösten Aufgaben
const calculateTaskPoints = (gameState) => {
  return gameState.playerTasks.reduce((sum, task) => sum + (task.points || 0), 0);
};

const StatsPanel = ({ gameState }) => {
  const playerScore = calculatePlayerScore(gameState);
  const longestSnake = findLongestSnake(gameState);
  const actualTurns = calculateActualTurns(gameState);
  const totalCardsPlayed = gameState.moves || 0;
  const taskPoints = calculateTaskPoints(gameState);
  const solvedTasksCount = gameState.playerTasks.length;
  
  // Gespielte Auxiliary-Karten zählen
  const usedRiskRewardCards = gameState.auxCards.riskRewardCards.filter(c => c.used).length;
  const usedComebackCard = gameState.auxCards.comebackCard.used ? 1 : 0;

  const mainStats = [
    { icon: Move, label: "Züge", value: `${actualTurns}/${totalCardsPlayed}`, colorClass: "text-blue-400", delay: 0.1 },
    { icon: Clock, label: "Zeit", value: gameState.timeElapsed, colorClass: "text-green-400", delay: 0.2 },
    { icon: Target, label: "Punkte", value: playerScore, colorClass: "text-red-400", delay: 0.3 },
    { icon: Zap, label: "Längste Schlange", value: `${longestSnake.length} (${longestSnake.owner})`, colorClass: "text-yellow-400", delay: 0.4 }
  ];

  const secondaryStats = [
    { icon: CheckCircle, label: "Aufgaben", value: `${solvedTasksCount} (${taskPoints}P)`, colorClass: "text-green-400", delay: 0.5 },
    { icon: Shield, label: "Risiko-Karten", value: usedRiskRewardCards, colorClass: "text-purple-400", delay: 0.6 },
    { icon: Trophy, label: "Comeback", value: usedComebackCard, colorClass: "text-orange-400", delay: 0.7 }
  ];

  return (
    <Card className="w-full max-w-5xl bg-slate-700/70 backdrop-blur-sm border-slate-600/50 text-white shadow-xl my-2">
      <CardHeader className="pb-1 pt-2">
        <CardTitle className="text-lg font-bold text-center text-purple-300">Spielübersicht</CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        {/* Hauptstatistiken */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mb-2">
          {mainStats.map(stat => (
            <StatItem key={stat.label} {...stat} />
          ))}
        </div>
        
        {/* Zusätzliche Statistiken */}
        <div className="grid grid-cols-3 gap-2">
          {secondaryStats.map(stat => (
            <StatItem key={stat.label} {...stat} small={true} />
          ))}
        </div>

        {/* DEBUG: KI-Status Anzeige */}
        {isDebugMode() && gameState.aiOpponentsData && gameState.aiOpponentsData.length > 0 && (
          <div className="bg-red-900/20 border border-red-700/50 p-2 rounded mb-2">
            <div className="text-xs font-bold text-red-400 mb-1">DEBUG: KI-Status</div>
            {gameState.aiOpponentsData.map(opponent => {
              const verdopplerActive = gameState.playedSpecialCardsHistory?.some(
                c => c.name === SPECIAL_CARDS_NAMES.DOUBLER && 
                     c.turnPlayed === gameState.turnNumber && 
                     c.playerId === opponent.id
              ) || false;
              
              return (
                <div key={opponent.id} className="text-xs text-slate-300 mb-1">
                  <span className="text-white font-semibold">G{opponent.id}:</span>
                  <span className="ml-1">{opponent.hand?.length || 0} Karten</span>
                  {opponent.isBlockedByGrube && <span className="ml-1 text-red-400">🚫BLOCKED</span>}
                  {verdopplerActive && <span className="ml-1 text-yellow-400">⚡VERDOPPLER</span>}
                  <span className="ml-1 text-slate-400">
                    F:{opponent.colorCardsPlayedThisTurn || 0} S:{opponent.specialCardsPlayedThisTurn || 0}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StatsPanel;