import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Zap, Clock, Target, Move, CheckCircle, Shield, Trophy } from 'lucide-react';
import { motion } from 'framer-motion';
import { CARD_TYPES, SPECIAL_CARDS_NAMES } from '@/utils/gameRules';
import { isDebugMode } from '@/utils/debugUtils.jsx';

const StatItem = ({ icon: Icon, label, value, colorClass, delay, small = false }) => (
  <motion.div
    className={`${small ? 'p-1' : 'p-2'} bg-slate-600/60 rounded-lg shadow-md`}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4, delay: delay }}
  >
    <Icon className={`${small ? 'h-4 w-4' : 'h-5 w-5'} mx-auto ${colorClass} mb-1`} />
    <p className={`${small ? 'text-xs' : 'text-sm'} text-slate-300`}>{label}</p>
    <p className={`${small ? 'text-sm' : 'text-lg'} font-semibold ${colorClass}`}>{value}</p>
  </motion.div>
);

// Hilfsfunktion um Farbgruppen-Punkte korrekt nach Schlangentanz-Regeln zu berechnen (mit Regenbogenschlange)
const calculateColorGroupPoints = (snakes) => {
  let points = 0;
  snakes.forEach(snake => {
    if (!snake || snake.length < 3) return; // Mindestens 3 Karten für eine gültige Schlange

    for (let i = 0; i < snake.length; i++) {
      if (snake[i].type === CARD_TYPES.COLOR || snake[i].name === 'Regenbogenschlange') {
        // Finde die beste Farbgruppe ab dieser Position
        let bestGroup = findBestColorGroup(snake, i);

        if (bestGroup && bestGroup.length >= 3) {
          let groupPoints = 0;

          // Berechne Punkte: Nur echte Farbkarten zählen, Regenbogenschlange = 0P
          bestGroup.cards.forEach(card => {
            if (card.name !== 'Regenbogenschlange') {
              groupPoints += card.points || 0;
            }
          });

          let multiplier = bestGroup.length === 3 ? 1 : bestGroup.length === 4 ? 2 : 3;
          points += groupPoints * multiplier;
          i += bestGroup.length - 1; // Überspringe verarbeitete Karten
        }
      }
    }
  });
  return points;
};

// Detaillierte Farbgruppen-Berechnung für Debug (mit korrekter Regenbogenschlange-Logik)
const calculateColorGroupPointsDetailed = (snake) => {
  if (!snake || snake.length < 3) {
    return {
      points: 0,
      details: `Zu kurz (${snake?.length || 0} Karten, min. 3 benötigt)`
    };
  }

  let points = 0;
  let details = [];

  for (let i = 0; i < snake.length; i++) {
    if (snake[i].type === CARD_TYPES.COLOR || snake[i].name === 'Regenbogenschlange') {
      const isRainbow = snake[i].name === 'Regenbogenschlange';
      const baseColor = isRainbow ? null : snake[i].color;

      // Finde die beste Farbgruppe ab dieser Position
      let bestGroup = findBestColorGroup(snake, i);

      if (bestGroup && bestGroup.length >= 3) {
        let groupPoints = 0;
        let rainbowCount = 0;
        let colorCards = 0;

        // Berechne Punkte: Nur echte Farbkarten zählen, Regenbogenschlange = 0P
        bestGroup.cards.forEach(card => {
          if (card.name === 'Regenbogenschlange') {
            rainbowCount++;
          } else {
            groupPoints += card.points || 0;
            colorCards++;
          }
        });

        let multiplier = bestGroup.length === 3 ? 1 : bestGroup.length === 4 ? 2 : 3;
        let groupScore = groupPoints * multiplier;
        points += groupScore;

        const groupDesc = rainbowCount > 0
          ? `${bestGroup.length}x ${bestGroup.color} (${colorCards} Farbe + ${rainbowCount} Regenbogen): ${groupPoints}P × ${multiplier} = ${groupScore}P`
          : `${bestGroup.length}x ${bestGroup.color}: ${groupPoints}P × ${multiplier} = ${groupScore}P`;

        details.push(groupDesc);
        i += bestGroup.length - 1; // Überspringe verarbeitete Karten
      }
    }
  }

  return {
    points,
    details: details.length > 0 ? details.join(', ') : 'Keine gültigen Farbgruppen (min. 3 gleiche)'
  };
};

// Hilfsfunktion: Finde die beste Farbgruppe ab einer Position (mit Regenbogenschlange als Joker)
const findBestColorGroup = (snake, startIndex) => {
  if (startIndex >= snake.length) return null;

  const startCard = snake[startIndex];
  const isStartRainbow = startCard.name === 'Regenbogenschlange';

  // Wenn Regenbogenschlange am Start, teste alle möglichen Farben
  if (isStartRainbow) {
    const colors = ['Blau', 'Rot', 'Gelb', 'Violett', 'Braun', 'Grün'];
    let bestGroup = null;

    for (const color of colors) {
      const group = buildColorGroup(snake, startIndex, color);
      if (!bestGroup || (group && group.length > bestGroup.length)) {
        bestGroup = group;
      }
    }
    return bestGroup;
  } else {
    // Normale Farbkarte: Baue Gruppe mit dieser Farbe
    return buildColorGroup(snake, startIndex, startCard.color);
  }
};

// Hilfsfunktion: Baue Farbgruppe für eine bestimmte Farbe
const buildColorGroup = (snake, startIndex, targetColor) => {
  const cards = [];

  for (let i = startIndex; i < snake.length; i++) {
    const card = snake[i];
    const isRainbow = card.name === 'Regenbogenschlange';
    const isMatchingColor = card.type === CARD_TYPES.COLOR && card.color === targetColor;

    if (isRainbow || isMatchingColor) {
      cards.push(card);
    } else {
      break; // Gruppe unterbrochen
    }
  }

  return cards.length >= 3 ? { color: targetColor, length: cards.length, cards } : null;
};

// Hilfsfunktion um Diversitäts-Bonus zu berechnen (korrekte Regenbogenschlange-Logik)
const calculateDiversityBonus = (snakes) => {
  let totalBonus = 0;
  snakes.forEach(snake => {
    if (!snake || snake.length < 3) return; // KORREKTUR: Mindestens 3 Karten für Diversitäts-Bonus

    const colorsInSnake = new Set();
    let rainbowCount = 0;

    snake.forEach(card => {
      if (card.type === CARD_TYPES.COLOR) {
        colorsInSnake.add(card.color);
      }
      // Regenbogenschlange: Zähle sie separat
      if (card.name === 'Regenbogenschlange') {
        rainbowCount++;
      }
    });

    // Für jede Regenbogenschlange: Wähle die beste noch nicht vorhandene Farbe
    const allColors = ['Blau', 'Rot', 'Gelb', 'Violett', 'Braun', 'Grün'];
    const availableColors = allColors.filter(color => !colorsInSnake.has(color));

    for (let i = 0; i < rainbowCount && availableColors.length > 0; i++) {
      const chosenColor = availableColors.shift();
      colorsInSnake.add(chosenColor);
    }

    const distinctColors = colorsInSnake.size;
    if (distinctColors === 2) totalBonus += 1;
    else if (distinctColors === 3) totalBonus += 2;
    else if (distinctColors === 4) totalBonus += 3;
    else if (distinctColors >= 5) totalBonus += 4;
  });
  return totalBonus;
};

// Detaillierte Diversitäts-Berechnung für Debug (korrekte Regenbogenschlange-Logik)
const calculateDiversityBonusDetailed = (snake) => {
  if (!snake || snake.length < 3) {
    return {
      points: 0,
      details: `Zu kurz für Diversität (${snake?.length || 0} Karten, min. 3 benötigt)`
    };
  }

  const colorsInSnake = new Set();
  let rainbowCount = 0;

  snake.forEach(card => {
    if (card.type === CARD_TYPES.COLOR) {
      colorsInSnake.add(card.color);
    }
    // Regenbogenschlange: Wähle optimale Farbe für Diversität
    if (card.name === 'Regenbogenschlange') {
      rainbowCount++;
    }
  });

  // Für jede Regenbogenschlange: Wähle die beste noch nicht vorhandene Farbe
  const allColors = ['Blau', 'Rot', 'Gelb', 'Violett', 'Braun', 'Grün'];
  const availableColors = allColors.filter(color => !colorsInSnake.has(color));

  for (let i = 0; i < rainbowCount && availableColors.length > 0; i++) {
    const chosenColor = availableColors.shift(); // Nimm die erste verfügbare Farbe
    colorsInSnake.add(chosenColor);
  }

  const distinctColors = colorsInSnake.size;
  let points = 0;

  if (distinctColors === 2) points = 1;
  else if (distinctColors === 3) points = 2;
  else if (distinctColors === 4) points = 3;
  else if (distinctColors >= 5) points = 4;

  const colorList = Array.from(colorsInSnake);
  const details = `${distinctColors} Farben (${colorList.join(', ')}${rainbowCount > 0 ? ` inkl. ${rainbowCount} Regenbogen` : ''}) = ${points}P`;

  return { points, details };
};

// Hilfsfunktion um Punkte korrekt nach Schlangentanz-Regeln zu berechnen
const calculatePlayerScore = (gameState) => {
  let totalScore = 0;

  // Punkte aus Farbgruppen (mindestens 3 aufeinanderfolgende gleiche Farben)
  totalScore += calculateColorGroupPoints(gameState.playerSnakes);

  // Diversitäts-Bonus für verschiedene Farben in einer Schlange
  totalScore += calculateDiversityBonus(gameState.playerSnakes);

  // Punkte aus gelösten Aufgaben (nur sichtbare Aufgaben)
  gameState.playerTasks.forEach(task => {
    if (task.isCompleted) {
      totalScore += task.points || 0;
    }
  });

  return totalScore;
};

// Hilfsfunktion um KI-Punkte zu berechnen
const calculateAiScore = (aiOpponent) => {
  let totalScore = 0;

  // Punkte aus Farbgruppen
  totalScore += calculateColorGroupPoints(aiOpponent.snakes || []);

  // Diversitäts-Bonus
  totalScore += calculateDiversityBonus(aiOpponent.snakes || []);

  // Punkte aus gelösten Aufgaben (nur sichtbare Aufgaben)
  if (aiOpponent.tasks) {
    aiOpponent.tasks.forEach(task => {
      if (task.isCompleted) {
        totalScore += task.points || 0;
      }
    });
  }

  return totalScore;
};

// Hilfsfunktion um die längste Schlange zu finden
const findLongestSnake = (gameState) => {
  let longestLength = 0;
  let longestOwner = '';

  // Prüfe Spieler-Schlangen
  gameState.playerSnakes.forEach((snake, index) => {
    if (snake.length > longestLength) {
      longestLength = snake.length;
      longestOwner = 'Du';
    }
  });

  // Prüfe KI-Gegner-Schlangen
  gameState.aiOpponentsData.forEach((opponent) => {
    opponent.snakes.forEach(snake => {
      if (snake.length > longestLength) {
        longestLength = snake.length;
        longestOwner = opponent.name;
      }
    });
  });

  return { length: longestLength, owner: longestOwner };
};

// Hilfsfunktion um echte Spielzüge zu berechnen
const calculateActualTurns = (gameState) => {
  // Verwende die neue playerActualTurns Eigenschaft
  return gameState.playerActualTurns || 0;
};

// Berechne Punkte aus gelösten Aufgaben
const calculateTaskPoints = (gameState) => {
  return gameState.playerTasks.reduce((sum, task) => sum + (task.points || 0), 0);
};

const StatsPanel = ({ gameState }) => {
  const playerScore = calculatePlayerScore(gameState);
  const longestSnake = findLongestSnake(gameState);
  const actualTurns = calculateActualTurns(gameState);
  const totalCardsPlayed = gameState.moves || 0;
  const taskPoints = calculateTaskPoints(gameState);
  const solvedTasksCount = gameState.playerTasks.length;

  // Gespielte Auxiliary-Karten zählen
  const usedRiskRewardCards = gameState.auxCards.riskRewardCards.filter(c => c.used).length;
  const usedComebackCard = gameState.auxCards.comebackCard.used ? 1 : 0;

  const mainStats = [
    { icon: Move, label: "Züge", value: `${actualTurns}/${totalCardsPlayed}`, colorClass: "text-blue-400", delay: 0.1 },
    { icon: Clock, label: "Zeit", value: gameState.timeElapsed, colorClass: "text-green-400", delay: 0.2 },
    { icon: Target, label: "Punkte", value: playerScore, colorClass: "text-red-400", delay: 0.3 },
    { icon: Zap, label: "Längste Schlange", value: `${longestSnake.length} (${longestSnake.owner})`, colorClass: "text-yellow-400", delay: 0.4 }
  ];

  const secondaryStats = [
    { icon: CheckCircle, label: "Aufgaben", value: `${solvedTasksCount} (${taskPoints}P)`, colorClass: "text-green-400", delay: 0.5 },
    { icon: Shield, label: "Risiko-Karten", value: usedRiskRewardCards, colorClass: "text-purple-400", delay: 0.6 },
    { icon: Trophy, label: "Comeback", value: usedComebackCard, colorClass: "text-orange-400", delay: 0.7 }
  ];

  return (
    <Card className="w-full max-w-5xl bg-slate-700/70 backdrop-blur-sm border-slate-600/50 text-white shadow-xl my-2">
      <CardHeader className="pb-1 pt-2">
        <CardTitle className="text-lg font-bold text-center text-purple-300">Spielübersicht</CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        {/* Hauptstatistiken */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mb-2">
          {mainStats.map(stat => (
            <StatItem key={stat.label} {...stat} />
          ))}
        </div>

        {/* Zusätzliche Statistiken */}
        <div className="grid grid-cols-3 gap-2">
          {secondaryStats.map(stat => (
            <StatItem key={stat.label} {...stat} small={true} />
          ))}
        </div>

        {/* Punkte-Übersicht für alle Spieler */}
        <div className="bg-green-900/20 border border-green-700/50 p-2 rounded mb-2">
          <div className="text-xs font-bold text-green-400 mb-2">🏆 AKTUELLE PUNKTE</div>

          {/* Menschlicher Spieler */}
          <div className="text-xs text-slate-300 mb-1 flex justify-between items-center">
            <span className="text-white font-semibold">Du:</span>
            <span className="text-green-300 font-bold">{calculatePlayerScore(gameState)} Punkte</span>
          </div>

          {/* KI-Gegner */}
          {gameState.aiOpponentsData && gameState.aiOpponentsData.map(opponent => {
            const aiScore = calculateAiScore(opponent);
            return (
              <div key={opponent.id} className="text-xs text-slate-300 mb-1 flex justify-between items-center">
                <span className="text-white font-semibold">Gegner {opponent.id}:</span>
                <span className="text-blue-300 font-bold">{aiScore} Punkte</span>
              </div>
            );
          })}
        </div>

        {/* DEBUG: KI-Status Anzeige */}
        {isDebugMode() && gameState.aiOpponentsData && gameState.aiOpponentsData.length > 0 && (
          <div className="bg-red-900/20 border border-red-700/50 p-2 rounded mb-2">
            <div className="text-xs font-bold text-red-400 mb-1">DEBUG: KI-Status</div>
            {gameState.aiOpponentsData.map(opponent => {
              const verdopplerActive = gameState.playedSpecialCardsHistory?.some(
                c => c.name === SPECIAL_CARDS_NAMES.DOUBLER &&
                     c.turnPlayed === gameState.turnNumber &&
                     c.playerId === opponent.id
              ) || false;

              return (
                <div key={opponent.id} className="text-xs text-slate-300 mb-1">
                  <span className="text-white font-semibold">G{opponent.id}:</span>
                  <span className="ml-1">{opponent.hand?.length || 0} Karten</span>
                  {opponent.isBlockedByGrube && <span className="ml-1 text-red-400">🚫BLOCKED</span>}
                  {verdopplerActive && <span className="ml-1 text-yellow-400">⚡VERDOPPLER</span>}
                  <span className="ml-1 text-slate-400">
                    F:{opponent.colorCardsPlayedThisTurn || 0} S:{opponent.specialCardsPlayedThisTurn || 0}
                  </span>
                </div>
              );
            })}
          </div>
        )}

        {/* DEBUG: Detaillierte Punkteberechnung und KI-Karten nebeneinander */}
        {isDebugMode() && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 mb-2">
            {/* Debug Punkteberechnung */}
            <div className="bg-yellow-900/20 border border-yellow-700/50 p-2 rounded">
              <div className="text-xs font-bold text-yellow-400 mb-2">🔍 DEBUG: PUNKTEBERECHNUNG</div>

            {/* Menschlicher Spieler */}
            <div className="mb-3">
              <div className="text-xs font-semibold text-white mb-1">Du:</div>
              {gameState.playerSnakes.map((snake, snakeIndex) => {
                const colorGroupPoints = calculateColorGroupPointsDetailed(snake);
                const diversityBonus = calculateDiversityBonusDetailed(snake);

                return (
                  <div key={snakeIndex} className="text-xs text-slate-300 ml-2 mb-1">
                    <span className="text-cyan-300">Schlange {snakeIndex + 1}:</span>
                    <span className="ml-1">Länge: {snake.length}</span>
                    <span className="ml-1 text-green-300">Farbgruppen: {colorGroupPoints.points}P</span>
                    <span className="ml-1 text-blue-300">Diversität: {diversityBonus.points}P</span>
                    {colorGroupPoints.details && (
                      <div className="ml-4 text-xs text-slate-400">{colorGroupPoints.details}</div>
                    )}
                    {diversityBonus.details && (
                      <div className="ml-4 text-xs text-slate-400">{diversityBonus.details}</div>
                    )}
                  </div>
                );
              })}
              <div className="text-xs text-slate-300 ml-2">
                <span className="text-purple-300">Aufgaben: {gameState.playerTasks.filter(t => t.isCompleted).length} abgeschlossen</span>
                <span className="ml-1 text-purple-300">({gameState.playerTasks.reduce((sum, t) => t.isCompleted ? sum + (t.points || 0) : sum, 0)}P)</span>
              </div>
            </div>

            {/* KI-Gegner */}
            {gameState.aiOpponentsData && gameState.aiOpponentsData.map(opponent => (
              <div key={opponent.id} className="mb-2">
                <div className="text-xs font-semibold text-white mb-1">Gegner {opponent.id}:</div>
                {(opponent.snakes || []).map((snake, snakeIndex) => {
                  const colorGroupPoints = calculateColorGroupPointsDetailed(snake);
                  const diversityBonus = calculateDiversityBonusDetailed(snake);

                  return (
                    <div key={snakeIndex} className="text-xs text-slate-300 ml-2 mb-1">
                      <span className="text-cyan-300">Schlange {snakeIndex + 1}:</span>
                      <span className="ml-1">Länge: {snake.length}</span>
                      <span className="ml-1 text-green-300">Farbgruppen: {colorGroupPoints.points}P</span>
                      <span className="ml-1 text-blue-300">Diversität: {diversityBonus.points}P</span>
                      {colorGroupPoints.details && (
                        <div className="ml-4 text-xs text-slate-400">{colorGroupPoints.details}</div>
                      )}
                      {diversityBonus.details && (
                        <div className="ml-4 text-xs text-slate-400">{diversityBonus.details}</div>
                      )}
                    </div>
                  );
                })}
                <div className="text-xs text-slate-300 ml-2">
                  <span className="text-purple-300">Aufgaben: {(opponent.tasks || []).filter(t => t.isCompleted).length} abgeschlossen</span>
                  <span className="ml-1 text-purple-300">({(opponent.tasks || []).reduce((sum, t) => t.isCompleted ? sum + (t.points || 0) : sum, 0)}P)</span>
                </div>
              </div>
            ))}
            </div>

            {/* Debug KI-Karten */}
            <div className="bg-cyan-900/20 border border-cyan-700/50 p-2 rounded">
              <div className="text-xs font-bold text-cyan-400 mb-2">🔍 DEBUG: GEGNER-KARTEN</div>
              {gameState?.aiOpponentsData?.map(opponent => (
                <div key={opponent.id} className="mb-3">
                  <div className="text-xs font-semibold text-white mb-1">
                    Gegner {opponent.id} ({opponent.hand?.length || 0} Karten):
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-1">
                    {(opponent.hand || []).map((card, index) => (
                      <div
                        key={card.id || index}
                        className={`
                          text-xs p-1 rounded border
                          ${card.type === 'COLOR'
                            ? `bg-${card.color}-800/50 border-${card.color}-500/50 text-${card.color}-200`
                            : 'bg-purple-800/50 border-purple-500/50 text-purple-200'
                          }
                        `}
                        title={`${card.name} (${card.type}, ${card.points || 0}P)`}
                      >
                        <div className="font-semibold truncate">{card.name}</div>
                        <div className="text-xs opacity-75">{card.points || 0}P</div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* DEBUG: KI-History Timeline */}
        {isDebugMode() && gameState.aiPlayHistory && gameState.aiPlayHistory.length > 0 && (
          <div className="bg-blue-900/20 border border-blue-700/50 p-3 rounded mb-2">
            <div className="text-xs font-bold text-blue-400 mb-3">
              🕒 KI-HISTORY TIMELINE - Total: {gameState.aiPlayHistory.length} Einträge
            </div>

            {/* Timeline Header */}
            <div className="mb-3">
              <div className="flex items-center space-x-2 mb-2">
                <div className="w-16 text-xs font-semibold text-blue-300">Spieler</div>
                {/* Dynamische Zug-Header */}
                {Array.from(new Set(gameState.aiPlayHistory.map(h => h.turnNumber)))
                  .sort((a, b) => a - b)
                  .slice(-8) // Zeige nur die letzten 8 Züge
                  .map(turnNum => (
                    <div key={turnNum} className="w-24 text-center">
                      <div className="text-xs font-bold text-yellow-300 bg-yellow-900/30 rounded px-1 py-0.5">
                        T{turnNum}
                      </div>
                    </div>
                  ))
                }
              </div>

              {/* Timeline Linie */}
              <div className="flex items-center">
                <div className="w-16"></div>
                {Array.from(new Set(gameState.aiPlayHistory.map(h => h.turnNumber)))
                  .sort((a, b) => a - b)
                  .slice(-8)
                  .map((turnNum, index) => (
                    <div key={turnNum} className="w-24 flex justify-center">
                      <div className="h-0.5 bg-blue-400 w-full relative">
                        <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-400 rounded-full"></div>
                      </div>
                    </div>
                  ))
                }
              </div>
            </div>

            {/* Spieler-Tabelle */}
            <div className="space-y-1">
              {gameState.aiOpponentsData?.map(opponent => {
                const playerHistory = gameState.aiPlayHistory.filter(h => h.playerId === opponent.id);
                const turns = Array.from(new Set(gameState.aiPlayHistory.map(h => h.turnNumber)))
                  .sort((a, b) => a - b)
                  .slice(-8);

                return (
                  <div key={opponent.id} className="flex items-center space-x-2">
                    {/* Spieler Name */}
                    <div className="w-16 text-xs font-semibold text-white bg-slate-700/50 rounded px-1 py-1 text-center">
                      G{opponent.id}
                    </div>

                    {/* Karten pro Zug */}
                    {turns.map(turnNum => {
                      const turnEntry = playerHistory.find(h => h.turnNumber === turnNum);

                      return (
                        <div key={turnNum} className="w-24 min-h-[2rem] bg-slate-800/30 rounded border border-slate-600/30 p-1">
                          {turnEntry ? (
                            turnEntry.blocked ? (
                              <div className="text-center text-orange-400 text-xs font-bold">
                                🕳️ BLOCK
                              </div>
                            ) : (
                              <div className="space-y-0.5">
                                {turnEntry.cards.map((card, cardIndex) => {
                                  // Bestimme Farb-Icon für Farbkarten
                                  const getColorIcon = (color) => {
                                    const colorMap = {
                                      'rot': '🔴', 'red': '🔴',
                                      'blau': '🔵', 'blue': '🔵',
                                      'grün': '🟢', 'green': '🟢',
                                      'gelb': '🟡', 'yellow': '🟡',
                                      'lila': '🟣', 'purple': '🟣', 'violett': '🟣',
                                      'orange': '🟠'
                                    };

                                    // Versuche direktes Mapping
                                    if (colorMap[color?.toLowerCase()]) {
                                      return colorMap[color.toLowerCase()];
                                    }

                                    // Fallback: CSS-Kreis mit Farbe
                                    const colorClass = {
                                      'rot': 'bg-red-500', 'red': 'bg-red-500',
                                      'blau': 'bg-blue-500', 'blue': 'bg-blue-500',
                                      'grün': 'bg-green-500', 'green': 'bg-green-500',
                                      'gelb': 'bg-yellow-500', 'yellow': 'bg-yellow-500',
                                      'lila': 'bg-purple-500', 'purple': 'bg-purple-500', 'violett': 'bg-purple-500',
                                      'orange': 'bg-orange-500'
                                    }[color?.toLowerCase()] || 'bg-gray-500';

                                    return (
                                      <div className={`w-3 h-3 rounded-full ${colorClass} mx-auto`} title={color}></div>
                                    );
                                  };

                                  // Bestimme Icon für die Karte
                                  const getCardIcon = () => {
                                    if (card.name === 'Regenbogenschlange') return '🌈';
                                    if (card.name === 'Verdoppler') return '⚡';
                                    if (card.name === 'Schlangengrube') return '🕳️';
                                    if (card.name === 'Farbendieb') return '🎭';
                                    if (card.name === 'Farbenschutz') return '🛡️';
                                    if (card.name === 'Farbenfusion') return '🔗';
                                    if (card.name === 'Schlangenhäutung') return '🐍';
                                    if (card.name === 'Schlangenfrass') return '🍽️';
                                    if (card.name === 'Schlangenblockade') return '🚧';
                                    if (card.name === 'Schlangenkorb') return '🧺';

                                    if (card.type === 'COLOR') {
                                      return getColorIcon(card.color);
                                    }

                                    return '🎴';
                                  };

                                  return (
                                    <div
                                      key={cardIndex}
                                      className={`text-xs px-1 py-0.5 rounded text-center flex items-center justify-center ${
                                        card.type === 'COLOR'
                                          ? 'bg-blue-700/50 text-blue-200'
                                          : 'bg-purple-700/50 text-purple-200'
                                      }`}
                                      title={`${card.name} (${card.type}${card.color ? `, Farbe: ${card.color}` : ''})`}
                                    >
                                      {getCardIcon()}
                                    </div>
                                  );
                                })}
                              </div>
                            )
                          ) : (
                            <div className="text-center text-gray-500 text-xs">-</div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>

            {/* Verdoppler-Analyse */}
            <div className="mt-2 pt-2 border-t border-blue-700/30">
              <div className="text-xs text-blue-300 mb-1 font-semibold">🔍 Verdoppler-Analyse:</div>
              <div className="text-xs space-y-1">
                {gameState.aiOpponentsData?.map(opponent => {
                  const playerHistory = gameState.aiPlayHistory.filter(h => h.playerId === opponent.id);
                  const verdopplerTurns = playerHistory.filter(h =>
                    h.cards.some(card => card.name === 'Verdoppler')
                  );

                  if (verdopplerTurns.length > 0) {
                    return (
                      <div key={opponent.id} className="bg-yellow-900/20 rounded px-2 py-1">
                        <span className="text-yellow-300 font-semibold">G{opponent.id}:</span>
                        {verdopplerTurns.map(turn => (
                          <span key={turn.turnNumber} className="ml-2">
                            T{turn.turnNumber} ({turn.cards.length} Karten: {turn.cards.map(c => c.name === 'Verdoppler' ? '⚡' : c.name === 'Regenbogenschlange' ? '🌈' : c.type === 'COLOR' ? '🔴' : '🎴').join('')})
                          </span>
                        ))}
                      </div>
                    );
                  }
                  return null;
                }).filter(Boolean)}
                {gameState.aiPlayHistory.filter(h => h.cards.some(card => card.name === 'Verdoppler')).length === 0 && (
                  <div className="text-gray-400">Noch keine Verdoppler gespielt</div>
                )}
              </div>
            </div>

            {/* Legende */}
            <div className="mt-3 pt-2 border-t border-blue-700/30">
              <div className="text-xs text-blue-300 mb-1 font-semibold">Legende:</div>
              <div className="grid grid-cols-2 gap-1 text-xs">
                <div className="space-y-1">
                  <div><span className="bg-blue-700/50 text-blue-200 px-1 rounded">Farbkarten:</span> 🔴🔵🟢🟡🟣🟠</div>
                  <div><span className="bg-purple-700/50 text-purple-200 px-1 rounded">Sonderkarten:</span> ⚡🌈🕳️🎭🛡️</div>
                </div>
                <div className="space-y-1">
                  <div><span className="text-orange-400">🕳️ = Blockiert</span> | <span className="text-gray-400">- = Kein Zug</span></div>
                  <div><span className="text-yellow-300">⚡ = Verdoppler (bis zu 3 Karten)</span></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Alte History für Vergleich (kann später entfernt werden) */}
        {false && isDebugMode() && gameState.aiPlayHistory && gameState.aiPlayHistory.length > 0 && (
          <div className="bg-gray-900/20 border border-gray-700/50 p-2 rounded mb-2">
            <div className="text-xs font-bold text-gray-400 mb-2">
              DEBUG: Alte KI-History (Vergleich)
            </div>
            <div className="max-h-32 overflow-y-auto">
              {gameState.aiPlayHistory
                .slice(-10)
                .map((historyEntry, index) => {
                  const { playerId, turnNumber, cards, blocked } = historyEntry;

                  return (
                    <div key={`${playerId}-${turnNumber}-${index}`} className="text-xs text-slate-300 mb-1 flex items-center">
                      <span className="text-white font-semibold min-w-[40px]">
                        T{turnNumber} G{playerId}:
                      </span>

                      {blocked ? (
                        <span className="ml-1 text-red-400 font-semibold">🚫 BLOCKIERT</span>
                      ) : cards.length === 0 ? (
                        <span className="ml-1 text-gray-400 italic">Passt</span>
                      ) : (
                        <div className="ml-1 flex flex-wrap gap-1">
                          {cards.map((card, cardIndex) => {
                            const isSpecial = card.type === CARD_TYPES.SPECIAL;
                            const cardColor = isSpecial ? 'text-purple-300' : 'text-blue-300';
                            const cardBg = isSpecial ? 'bg-purple-800/30' : 'bg-blue-800/30';

                            // Kürze Kartennamen für bessere Anzeige
                            const shortName = card.name.length > 8 ?
                              card.name.substring(0, 8) + '...' : card.name;

                            return (
                              <span
                                key={cardIndex}
                                className={`${cardColor} ${cardBg} px-1 py-0.5 rounded text-xs border border-opacity-50`}
                                title={`${card.name} (${card.type}${card.color ? `, ${card.color}` : ''})`}
                              >
                                {shortName}
                              </span>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StatsPanel;