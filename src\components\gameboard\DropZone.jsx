import React from 'react';
import { useDrop } from 'react-dnd';

const ItemTypes = {
  CARD: 'card',
};

const cn = (...inputs) => inputs.filter(Boolean).join(' ');

const DropZone = ({ onDrop, onClick, zoneName, children, className, isActive, acceptItemType = ItemTypes.CARD }) => {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: acceptItemType,
    drop: (item) => {
      if (onDrop) onDrop(item, zoneName);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop(),
    }),
  }));

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <div
      ref={drop}
      onClick={handleClick}
      className={cn(
        "p-2 sm:p-4 border-2 border-dashed rounded-lg min-h-[80px] sm:min-h-[100px] flex items-center justify-center transition-all duration-300 ease-in-out",
        className,
        isActive && isOver && canDrop && "bg-green-500/30 border-green-400 scale-105 shadow-lg",
        isActive && isOver && !canDrop && "bg-red-500/30 border-red-400",
        isActive && !isOver && canDrop && "border-slate-400 hover:border-slate-300 bg-slate-700/50",
        !isActive && "bg-slate-800/30 border-slate-700 cursor-not-allowed opacity-70"
      )}
      role={onClick ? "button" : undefined}
      tabIndex={onClick && isActive ? 0 : undefined}
      onKeyDown={(e) => {
        if (onClick && isActive && (e.key === 'Enter' || e.key === ' ')) {
          handleClick();
        }
      }}
    >
      {children}
    </div>
  );
};

export default DropZone;
