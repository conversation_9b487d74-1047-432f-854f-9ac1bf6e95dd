import Phaser from 'phaser';

class GameScene extends Phaser.Scene {
  constructor() {
    super({ key: 'GameScene' });
  }

  preload() {
    this.load.image('sky', 'assets/skies/space3.png');
    this.load.image('logo', 'assets/phaser3-logo.png');
    this.load.image('red', 'assets/particles/red.png');

    // Platzhalter für Karten-Assets
    // Beispiel: this.load.image('card_back', '/assets/cards/card_back.png');
    // for (let i = 1; i <= 52; i++) {
    //   this.load.image(`card_${i}`, `/assets/cards/card_${i}.png`);
    // }
    console.log("Phaser: Preloading assets...");
  }

  create() {
    this.add.image(400, 300, 'sky');

    const particles = this.add.particles(0, 0, 'red', {
      speed: 100,
      scale: { start: 1, end: 0 },
      blendMode: 'ADD'
    });

    const logo = this.physics.add.image(400, 100, 'logo');

    logo.setVelocity(100, 200);
    logo.setBounce(1, 1);
    logo.setCollideWorldBounds(true);

    particles.startFollow(logo);

    this.add.text(10, 10, 'Phaser Spiel initialisiert!', { font: '32px Arial', fill: '#ffffff' });
    console.log("Phaser: Scene created.");
  }

  update() {
     // Spiel-Logik hier
  }
}

const initializeGame = (parentElementId) => {
  const config = {
    type: Phaser.AUTO,
    width: 800,
    height: 600,
    parent: parentElementId, // ID des div-Elements
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 200 }
      }
    },
    scene: [GameScene],
    backgroundColor: '#000033', // Dunkelblauer Hintergrund als Fallback
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH
    }
  };

  // Überprüfen, ob bereits ein Spiel auf diesem Element läuft
  if (Phaser.GAMES.length > 0) {
    const existingGame = Phaser.GAMES.find(game => game.config.parent === parentElementId);
    if (existingGame) {
      console.warn("Phaser: Spielinstanz existiert bereits für dieses Element. Zerstöre alte Instanz.");
      existingGame.destroy(true);
    }
  }
  
  console.log("Phaser: Initializing new game instance...");
  return new Phaser.Game(config);
};

export default initializeGame;