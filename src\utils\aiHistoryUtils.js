import { debugLog, debugWarn, debugError } from './debugUtils.jsx';

/**
 * Bereinigt die KI-History von Duplikaten und ungültigen Einträgen
 * @param {Array} aiPlayHistory - Die aktuelle KI-History
 * @returns {Array} - Bereinigte History
 */
export const cleanAiPlayHistory = (aiPlayHistory) => {
  if (!Array.isArray(aiPlayHistory)) {
    debugWarn('[AI-History] Invalid history format, returning empty array');
    return [];
  }

  const cleanedHistory = [];
  const seenEntries = new Set();

  for (const entry of aiPlayHistory) {
    if (!entry || typeof entry !== 'object') {
      debugWarn('[AI-History] Skipping invalid entry:', entry);
      continue;
    }

    const { playerId, turnNumber, cards, blocked } = entry;

    // Erstelle eindeutigen Schlüssel für diesen Zug
    const entryKey = `${playerId}-${turnNumber}`;

    if (seenEntries.has(entryKey)) {
      debugWarn(`[AI-History] Duplicate entry found for Player ${playerId}, Turn ${turnNumber} - merging cards`);

      // Finde existierenden Eintrag und merge Karten
      const existingEntry = cleanedHistory.find(e => e.playerId === playerId && e.turnNumber === turnNumber);
      if (existingEntry && Array.isArray(cards)) {
        // Merge Karten ohne Duplikate
        for (const card of cards) {
          const isDuplicate = existingEntry.cards.some(existingCard =>
            existingCard.name === card.name &&
            existingCard.type === card.type &&
            existingCard.color === card.color
          );

          if (!isDuplicate) {
            existingEntry.cards.push(card);
          }
        }
      }
      continue;
    }

    // Validiere Karten-Array
    const validCards = Array.isArray(cards) ? cards.filter(card =>
      card && typeof card === 'object' && card.name
    ) : [];

    cleanedHistory.push({
      playerId: playerId || 0,
      turnNumber: turnNumber || 0,
      cards: validCards,
      blocked: Boolean(blocked),
      timestamp: entry.timestamp || new Date().toISOString()
    });

    seenEntries.add(entryKey);
  }

  debugLog(`[AI-History] Cleaned history: ${aiPlayHistory.length} -> ${cleanedHistory.length} entries`);
  return cleanedHistory;
};

/**
 * Fügt eine Karte zur KI-History hinzu (mit Duplikat-Schutz)
 * @param {Array} currentHistory - Aktuelle History
 * @param {number} playerId - Spieler ID
 * @param {number} turnNumber - Zug Nummer
 * @param {Object} card - Karte die gespielt wurde
 * @returns {Array} - Aktualisierte History
 */
export const addCardToAiHistory = (currentHistory, playerId, turnNumber, card) => {
  const newHistory = [...currentHistory];
  const existingEntryIndex = newHistory.findIndex(
    h => h.playerId === playerId && h.turnNumber === turnNumber
  );

  const newCard = {
    name: card.name,
    type: card.type,
    color: card.color,
    timestamp: new Date().toISOString()
  };

  // Debug: Zeige Kartendetails für Farbkarten
  if (card.type === 'COLOR') {
    console.log(`🎨 [AI-History] Farbkarte: "${card.name}" | Farbe: "${card.color}" | Typ: ${card.type}`);
  }

  if (existingEntryIndex >= 0) {
    // Prüfe auf Duplikate
    const existingCards = newHistory[existingEntryIndex].cards;
    const isDuplicate = existingCards.some(existingCard =>
      existingCard.name === newCard.name &&
      existingCard.type === newCard.type &&
      existingCard.color === newCard.color &&
      Math.abs(new Date(existingCard.timestamp) - new Date(newCard.timestamp)) < 2000 // 2 Sekunden Toleranz
    );

    if (!isDuplicate) {
      newHistory[existingEntryIndex].cards.push(newCard);
      debugLog(`[AI-History] Card added: ${newCard.name} for Player ${playerId}, Turn ${turnNumber}`);
    } else {
      debugWarn(`[AI-History] Duplicate prevented: ${newCard.name} for Player ${playerId}`);
    }
  } else {
    // Neuer Eintrag
    newHistory.push({
      playerId,
      turnNumber,
      cards: [newCard],
      blocked: false,
      timestamp: new Date().toISOString()
    });
    debugLog(`[AI-History] New turn created for Player ${playerId}, Turn ${turnNumber}`);
  }

  return newHistory;
};

/**
 * Fügt eine Blockierung zur KI-History hinzu
 * @param {Array} currentHistory - Aktuelle History
 * @param {number} playerId - Spieler ID
 * @param {number} turnNumber - Zug Nummer
 * @returns {Array} - Aktualisierte History
 */
export const addBlockingToAiHistory = (currentHistory, playerId, turnNumber) => {
  const newHistory = [...currentHistory];
  const existingEntryIndex = newHistory.findIndex(
    h => h.playerId === playerId && h.turnNumber === turnNumber
  );

  if (existingEntryIndex === -1) {
    newHistory.push({
      playerId,
      turnNumber,
      cards: [],
      blocked: true,
      timestamp: new Date().toISOString()
    });
    debugLog(`[AI-History] Blocking added for Player ${playerId}, Turn ${turnNumber}`);
  } else {
    debugWarn(`[AI-History] Blocking already exists for Player ${playerId}, Turn ${turnNumber}`);
  }

  return newHistory;
};

/**
 * Schreibt History-Debug-Informationen in Log-Datei
 * @param {Array} aiPlayHistory - KI-History
 * @param {string} context - Kontext der Ausgabe
 */
export const logAiHistoryDebug = (aiPlayHistory, context = '') => {
  const historyStats = aiPlayHistory.reduce((stats, entry) => {
    const key = `Player${entry.playerId}`;
    if (!stats[key]) {
      stats[key] = { cards: 0, blocked: 0, turns: 0 };
    }
    stats[key].turns++;
    stats[key].cards += entry.cards.length;
    if (entry.blocked) stats[key].blocked++;
    return stats;
  }, {});

  debugLog(`[AI-History-Stats] ${context}:`, historyStats);

  // Detaillierte Ausgabe der letzten 5 Einträge
  const recent = aiPlayHistory.slice(-5);
  debugLog(`[AI-History-Recent] Last 5 entries:`, recent.map(entry => ({
    player: entry.playerId,
    turn: entry.turnNumber,
    cardCount: entry.cards.length,
    cardNames: entry.cards.map(c => c.name),
    blocked: entry.blocked
  })));
};
