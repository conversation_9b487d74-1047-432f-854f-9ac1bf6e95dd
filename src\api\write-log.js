import fs from 'fs';
import path from 'path';

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { content, filename } = req.body;

    if (!content || !filename) {
      return res.status(400).json({ error: 'Content and filename required' });
    }

    // Stelle sicher, dass logs Verzeichnis existiert
    const logsDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    // Schreibe Log-Datei
    const filePath = path.join(logsDir, filename);
    fs.writeFileSync(filePath, content, 'utf8');

    console.log(`✅ Log-Datei geschrieben: ${filePath}`);
    
    return res.status(200).json({ 
      success: true, 
      message: 'Log file written successfully',
      path: filePath 
    });

  } catch (error) {
    console.error('❌ <PERSON><PERSON> beim Schreiben der Log-Datei:', error);
    return res.status(500).json({ 
      error: 'Failed to write log file',
      details: error.message 
    });
  }
}
