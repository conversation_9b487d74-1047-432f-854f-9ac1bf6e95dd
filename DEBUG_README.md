# 🐍 Debug-Modus für Schlangentanz Entwicklung

## Setup

Erstelle eine `.env` Datei im Projektroot mit:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key

# Debug-Modus aktivieren/deaktivieren
VITE_DEBUG_MODE=true
```

## Debug-Features

### 🔍 **KI-Karten Anzeige**
- Zeigt alle KI-Handkarten wie deine eigenen an
- Kartentyp, Farbe und Punkte sichtbar
- Nur im Debug-Modus aktiv

### 📊 **Erweiterte Konsolen-Logs**
- Detaillierte KI-Entscheidungen
- Verdoppler-Status Tracking
- Blockierungs-Mechanismen
- Spielzug-Analysen

### 🛠️ **Debug-Panel (oben rechts)**
- **Quick Actions:**
  - KI Verdoppler geben
  - KI blockieren
  - Spielzustand dumpen
- Deck/Discard Info
- Spielphase <PERSON>

### 📈 **KI-Status Panel**
- Karten<PERSON>hl pro KI
- Blockierungs-Status (🚫BLOCKED)
- Verdoppler-Status (⚡VERDOPPLER)
- Akt<PERSON><PERSON> (F:2 S:1)

## Produktions-Setup

Für die Produktionsversion:

```env
VITE_DEBUG_MODE=false
```

Alle Debug-Features werden automatisch deaktiviert:
- ❌ Keine KI-Karten sichtbar
- ❌ Keine Debug-Konsolen-Logs
- ❌ Kein Debug-Panel
- ❌ Kein KI-Status Panel

## Test-Szenarien

1. **Verdoppler testen:**
   - Debug-Panel → "KI Verdoppler geben"
   - Schaue KI-Status für ⚡Symbol
   - Beobachte KI spielt 3 Karten

2. **Schlangengrube testen:**
   - Debug-Panel → "KI blockieren"
   - Schaue KI-Status für 🚫Symbol  
   - KI setzt 2 Runden aus

3. **Kartenanalyse:**
   - Sieh KI-Handkarten in gelber Debug-Box
   - Verfolge Entscheidungen in Konsole
   - Verstehe KI-Strategie

## Environment Detection

Der Debug-Modus ist automatisch aktiv wenn:
- `VITE_DEBUG_MODE=true` ODER
- Vite Development Mode (`import.meta.env.DEV`)

Dies stellt sicher, dass in der Produktion niemals Debug-Informationen angezeigt werden. 