{"name": "simple-react-app", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Tests not yet configured\" && exit 0"}, "dependencies": {"@ngrok/ngrok-win32-x64-msvc": "^1.5.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@smithery/cli": "^1.2.1", "@supabase/supabase-js": "^2.39.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.4", "gsap": "3.11.0", "lucide-react": "^0.292.0", "phaser": "3.60.0", "react": "^18.2.0", "react-dnd": "14.0.0", "react-dnd-html5-backend": "14.0.0", "react-dom": "^18.2.0", "react-router-dom": "6.11.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "rollup": "^3.29.4", "tailwindcss": "^3.3.3", "vite": "^4.5.0"}, "main": "postcss.config.js", "keywords": [], "author": "", "license": "ISC", "description": ""}