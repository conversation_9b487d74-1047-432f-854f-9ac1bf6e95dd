import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, LogOut, Settings, ShieldQuestion, Menu as MenuIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const GameBoardHeader = ({ onStartGame, onEndTurn, gameState, canEndTurn }) => {
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  const isEndTurnDisabled = !canEndTurn || !gameState.isPlayerTurn || gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring';

  return (
    <header className="w-full p-2 sm:p-3 md:p-4 bg-slate-800/70 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-2 sm:space-x-3">
          <Button variant="ghost" size="icon" onClick={() => navigate('/main-menu')} className="text-slate-300 hover:text-primary-gold hover:bg-slate-700/50">
            <MenuIcon className="h-5 w-5 sm:h-6 sm:w-6" />
          </Button>
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-primary-gold font-heading tracking-wider">ColorConda</h1>
        </div>
        <div className="flex items-center space-x-1 sm:space-x-2">
          <Button
            onClick={onEndTurn}
            disabled={isEndTurnDisabled}
            variant="action"
            size="sm"
            className={`text-sm sm:text-base px-2 py-1 sm:px-3 sm:py-1.5 ${
              gameState?.isBlockedByGrube ? 'bg-orange-600 hover:bg-orange-700 border-orange-500' : ''
            }`}
          >
            {gameState?.isBlockedByGrube ? '🕳️ Aussetzen' : 'Zug beenden'}
          </Button>
          <Button
            onClick={onStartGame}
            variant="outline"
            size="sm"
            className="text-slate-300 border-slate-600 hover:bg-slate-700/50 hover:text-primary-gold text-sm sm:text-base px-2 py-1 sm:px-3 sm:py-1.5"
          >
            <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" /> Neustart
          </Button>
          <Button variant="ghost" size="icon" onClick={() => navigate('/tutorial')} className="text-slate-300 hover:text-primary-gold hover:bg-slate-700/50">
            <ShieldQuestion className="h-5 w-5 sm:h-6 sm:w-6" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => navigate('/settings')} className="text-slate-300 hover:text-primary-gold hover:bg-slate-700/50">
            <Settings className="h-5 w-5 sm:h-6 sm:w-6" />
          </Button>
          <Button variant="ghost" size="icon" onClick={handleLogout} className="text-slate-300 hover:text-red-400 hover:bg-slate-700/50">
            <LogOut className="h-5 w-5 sm:h-6 sm:w-6" />
          </Button>
        </div>
      </div>
    </header>
  );
};

export default GameBoardHeader;