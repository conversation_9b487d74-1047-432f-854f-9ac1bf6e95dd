import React from 'react';
import { motion } from 'framer-motion';
import SnakeDisplay from '@/components/gameboard/SnakeDisplay';
import OpponentDisplay from '@/components/gameboard/OpponentDisplay.jsx';
import DropZone from '@/components/gameboard/DropZone';
import { Button } from '@/components/ui/button';
import { PlusCircle, Layers, Archive, Users } from 'lucide-react';

const GameArea = ({ 
  playerSnakes, 
  aiOpponentsData,
  activeSnakeIndex,
  isPlayerTurn, 
  currentPlayerId,
  selectedCardForPlay, 
  discardPileLength, 
  deckLength,
  onDropOnSnake, 
  onClickOnSnakePosition,
  onDropOnDiscard,
  onStartNewSnake,
  onSetActiveSnakeIndex,
  gamePhase,
  className
}) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { staggerChildren: 0.1, delayChildren: 0.2 }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  return (
    <motion.div 
      className={`bg-slate-800/50 backdrop-blur-sm p-2 sm:p-4 rounded-xl shadow-2xl border border-slate-700/50 flex flex-col ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="flex justify-between items-center mb-2 sm:mb-4">
        <h2 className="text-xl sm:text-2xl font-heading text-primary-gold">Spielfeld</h2>
        {isPlayerTurn && playerSnakes.length < 2 && gamePhase === 'playing' && (
          <Button onClick={onStartNewSnake} size="sm" variant="outline" className="text-sky-300 border-sky-500 hover:bg-sky-500/20">
            <PlusCircle className="h-4 w-4 mr-2" /> Neue Schlange
          </Button>
        )}
      </div>

      <div className="flex-grow space-y-2 sm:space-y-3 overflow-y-auto p-1 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-700/50">
        
        <motion.div variants={itemVariants}>
            <div className="flex items-center mb-1">
                <Users className={`h-5 w-5 mr-2 ${isPlayerTurn && currentPlayerId === 0 ? 'text-primary-gold' : 'text-slate-400'}`} />
                <h3 className={`text-lg font-semibold ${isPlayerTurn && currentPlayerId === 0 ? 'text-primary-gold' : 'text-slate-300'}`}>Deine Schlangen</h3>
            </div>
            {playerSnakes.map((snake, index) => (
            <motion.div 
                key={`player-snake-area-${index}`} 
                variants={itemVariants} 
                onClick={() => isPlayerTurn && onSetActiveSnakeIndex(index)}
                className={`p-1.5 sm:p-2 rounded-lg transition-all duration-300 ease-in-out ${isPlayerTurn ? 'cursor-pointer' : ''}
                            ${index === activeSnakeIndex && isPlayerTurn && currentPlayerId === 0 ? 'bg-primary-dark-green/50 border-2 border-primary-gold shadow-lg' : 'bg-slate-700/60 border border-slate-600 hover:bg-slate-600/80'}`}
            >
                <h4 className="text-xs sm:text-sm font-semibold text-slate-300 mb-1">
                Schlange {index + 1} {index === activeSnakeIndex && isPlayerTurn && currentPlayerId === 0 && <span className="text-xs text-primary-gold">(Aktiv)</span>}
                </h4>
                <SnakeDisplay 
                snake={snake} 
                snakeId={`player-${index}`}
                onDrop={(item, position) => onDropOnSnake(item, 'Schlangenzone', index, position)} 
                onClickOnPosition={(position) => onClickOnSnakePosition(index, position)}
                isPlayerTurn={isPlayerTurn && currentPlayerId === 0}
                selectedCardId={selectedCardForPlay?.id}
                isActive={index === activeSnakeIndex && isPlayerTurn && currentPlayerId === 0}
                />
            </motion.div>
            ))}
        </motion.div>

        
        {aiOpponentsData && aiOpponentsData.map(opponent => (
          <OpponentDisplay 
            key={`opponent-display-${opponent.id}`}
            opponentData={opponent}
            isActivePlayer={!isPlayerTurn && currentPlayerId === opponent.id}
          />
        ))}
      </div>

      <motion.div variants={itemVariants} className="grid grid-cols-2 gap-2 sm:gap-4 mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-slate-700">
        <div 
          className="bg-slate-700/80 p-2 sm:p-3 rounded-lg flex flex-col items-center justify-center min-h-[60px] sm:min-h-[80px]"
        >
          <Layers className="h-5 w-5 sm:h-6 sm:w-6 text-sky-400 mb-1" />
          <p className="text-xs sm:text-sm text-slate-300">Nachziehstapel</p>
          <p className="text-sm sm:text-base font-bold text-sky-300">{deckLength}</p>
        </div>
        <DropZone 
          onDrop={onDropOnDiscard} 
          zoneName="discard-pile"
          className="bg-slate-700/80 p-2 sm:p-3 rounded-lg flex flex-col items-center justify-center min-h-[60px] sm:min-h-[80px]"
          isActive={isPlayerTurn && currentPlayerId === 0} 
        >
          <Archive className="h-5 w-5 sm:h-6 sm:w-6 text-red-400 mb-1" />
          <p className="text-xs sm:text-sm text-slate-300">Ablagestapel</p>
          <p className="text-sm sm:text-base font-bold text-red-300">{discardPileLength}</p>
        </DropZone>
      </motion.div>
      {gamePhase === 'gameOver' && (
        <motion.div 
            variants={itemVariants} 
            className="mt-4 p-4 bg-primary-gold/80 text-slate-900 rounded-lg text-center font-bold text-xl"
            initial={{opacity:0, y:20}} animate={{opacity:1, y:0}} transition={{delay:0.5}}
        >
            Spiel beendet!
        </motion.div>
      )}
       {gamePhase === 'lastRound' && (
        <motion.div 
            variants={itemVariants} 
            className="mt-4 p-3 bg-red-600/80 text-white rounded-lg text-center font-semibold text-lg"
            initial={{opacity:0, y:20}} animate={{opacity:1, y:0}} transition={{delay:0.5}}
        >
            LETZTE RUNDE!
        </motion.div>
      )}
    </motion.div>
  );
};

export default GameArea;