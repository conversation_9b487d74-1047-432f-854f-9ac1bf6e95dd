import { useCallback } from 'react';
import { useToast } from "@/components/ui/use-toast";
import { 
  applyCardEffect, 
  handleModalAction, 
  checkTaskCompletionLocal,
  handleEndTurn,
  canActivateComebackCard,
} from '@/utils/gameLogic.jsx';
import { CARD_TYPES, SPECIAL_CARDS_NAMES } from '@/utils/gameRules';

export const usePlayerActions = (gameState, setGameState) => {
  const { toast } = useToast();

  const playSelectedCardLogic = useCallback((cardToPlayParam, targetZone, positionInSnake = 'end', isAuxAction = false, targetSnakeIndexForDrop = null, forPlayerId = 0) => {
    setGameState(prevGameState => {
      const cardToPlay = { ...cardToPlayParam };
      
      const isCurrentPlayerAi = forPlayerId !== 0;
      const playerState = isCurrentPlayerAi ? prevGameState.aiOpponentsData.find(opp => opp.id === forPlayerId) : prevGameState;
      
      if(!playerState && isCurrentPlayerAi) {
        console.error(`[PlayLogic] AI Player ${forPlayerId} not found in gameState.`);
        return {...prevGameState, toastInfoForDisplay: { title: "Interner Fehler", description: `KI Spieler ${forPlayerId} nicht gefunden.`, variant: "destructive" }};
      }

      const playerHand = isCurrentPlayerAi ? playerState.hand : prevGameState.playerHand;
      const playerSnakes = isCurrentPlayerAi ? playerState.snakes : prevGameState.playerSnakes;
      const activePlayerSnakeIndex = isCurrentPlayerAi ? playerState.activeSnakeIndex : prevGameState.activeSnakeIndex;

      const prevColorPlayed = playerState.colorCardsPlayedThisTurn || 0;
      const prevSpecialPlayed = playerState.specialCardsPlayedThisTurn || 0;

      let localToastInfo = null;

      if (!cardToPlay || !cardToPlay.id) {
        localToastInfo = { title: "Fehler beim Spielen", description: "Keine gültige Karte zum Spielen übergeben.", variant: "destructive" };
        return {...prevGameState, toastInfoForDisplay: localToastInfo};
      }

      if (prevGameState.gamePhase === 'gameOver' || prevGameState.gamePhase === 'scoring' || (forPlayerId === 0 && !prevGameState.isPlayerTurn)) {
         localToastInfo = { title: "Aktion nicht möglich", description: "Spiel beendet oder nicht am Zug.", variant: "destructive" };
        return {...prevGameState, toastInfoForDisplay: localToastInfo};
      }
  
      const cardExistsInCurrentHand = playerHand.find(c => c && c.id === cardToPlay.id);
      if (!cardExistsInCurrentHand) {
          localToastInfo = { title: "Fehler beim Spielen", description: `Karte "${cardToPlay.name || cardToPlay.text}" nicht (mehr) in der Hand.`, variant: "destructive" };
          return {...prevGameState, toastInfoForDisplay: localToastInfo};
      }
      
      const verdopplerInHistory = prevGameState.playedSpecialCardsHistory.some(
        c => c.name === SPECIAL_CARDS_NAMES.DOUBLER && c.turnPlayed === prevGameState.turnNumber && c.playerId === forPlayerId
      );
      
      const justPlayedVerdoppler = prevSpecialPlayed > 0 && prevGameState.playedSpecialCardsHistory.some(
        c => c.name === SPECIAL_CARDS_NAMES.DOUBLER && c.turnPlayed === prevGameState.turnNumber && c.playerId === forPlayerId
      );
      
      const isVerdopplerActive = verdopplerInHistory || justPlayedVerdoppler;
      
      const isSchlangenkorbActive = isCurrentPlayerAi ? false : prevGameState.schlangenkorbPlayState.active; 
      const isComebackActive = isCurrentPlayerAi ? false : prevGameState.comebackActive; 
      const isBlockedByGrubeAi = isCurrentPlayerAi ? playerState.isBlockedByGrube : false;

      if (!isAuxAction) {
        if (isVerdopplerActive) {
            if (prevColorPlayed + prevSpecialPlayed >= 3) {
                localToastInfo = { title: "Limit erreicht (Verdoppler)", description: "Bereits 3 Karten mit Verdoppler gespielt.", variant: "destructive" };
                return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
            }
        } else if (isSchlangenkorbActive) {
             if (prevGameState.schlangenkorbPlayState.playedCount >= prevGameState.schlangenkorbPlayState.cardsToPlay) {
                localToastInfo = { title: "Limit erreicht (Schlangenkorb)", description: `Bereits ${prevGameState.schlangenkorbPlayState.cardsToPlay} Karten aus dem Korb gespielt.`, variant: "destructive" };
                return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
            }
        } else if (isComebackActive) {
            if (prevGameState.comebackActionsRemaining <= 0) {
                localToastInfo = { title: "Limit erreicht (Comeback)", description: "Keine Comeback-Aktionen mehr.", variant: "destructive" };
                return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
            }
        } else {
            if (cardToPlay.type === CARD_TYPES.COLOR && prevColorPlayed >= 1) {
                localToastInfo = { title: "Limit Farbkarte", description: "Bereits eine Farbkarte in diesem Zug gespielt.", variant: "destructive" };
                return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
            }
            if (cardToPlay.type === CARD_TYPES.SPECIAL && prevSpecialPlayed >= 1) {
                localToastInfo = { title: "Limit Sonderkarte", description: "Bereits eine Sonderkarte in diesem Zug gespielt.", variant: "destructive" };
                return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
            }
        }
        if (isBlockedByGrubeAi && cardToPlay.type === CARD_TYPES.COLOR) {
             localToastInfo = { title: "Blockiert!", description: `Gegner ${forPlayerId} ist durch Schlangengrube blockiert und kann keine Farbkarten spielen.`, variant: "destructive" };
             return {...prevGameState, toastInfoForDisplay: localToastInfo};
        }
      }
      
      const mustPlaySpecial = isCurrentPlayerAi ? playerState.mustPlaySpecialCard : prevGameState.mustPlaySpecialCard;
      if (mustPlaySpecial && cardToPlay.type !== CARD_TYPES.SPECIAL && !isAuxAction) {
        localToastInfo = { title: "Sonderkarte spielen!", description: "Du musst eine Sonderkarte spielen.", variant: "destructive" };
        return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
      }
  
      let newPlayerHandState = [...playerHand];
      let newPlayerSnakesState = playerSnakes.map(s => [...s]);
      let newDiscardPile = [...prevGameState.discardPile];
      let newColorCardsPlayedThisTurn = prevColorPlayed;
      let newSpecialCardsPlayedThisTurn = prevSpecialPlayed;
      let newMoves = prevGameState.moves;
      let newModalRequired = prevGameState.modalRequired; 
      let newSchlangenkorbPlayState = { ...prevGameState.schlangenkorbPlayState };
      let newComebackActive = prevGameState.comebackActive;
      let newComebackActionsRemaining = prevGameState.comebackActionsRemaining;
      let newMustPlaySpecialCardState = mustPlaySpecial;
      let newActiveSnakeIndexToUse = targetSnakeIndexForDrop !== null ? targetSnakeIndexForDrop : activePlayerSnakeIndex;
      let newPlayedSpecialCardsHistory = [...prevGameState.playedSpecialCardsHistory];
      let effectResult = null;

      if (cardToPlay.type === CARD_TYPES.COLOR) {
        if (targetZone === 'Schlangenzone') {
          let snakeToPlaceInIdx = newActiveSnakeIndexToUse;
          if (!newPlayerSnakesState[snakeToPlaceInIdx]) newPlayerSnakesState[snakeToPlaceInIdx] = [];
          let targetSnakeArray = [...newPlayerSnakesState[snakeToPlaceInIdx]];
          
          if (positionInSnake === 'start') targetSnakeArray.unshift(cardToPlay); else targetSnakeArray.push(cardToPlay);
          
          newPlayerSnakesState[snakeToPlaceInIdx] = targetSnakeArray;
          newPlayerHandState = newPlayerHandState.filter(c => c && c.id !== cardToPlay.id);
          
          if (!isAuxAction) newColorCardsPlayedThisTurn += 1;
          
          if (newSchlangenkorbPlayState.active && !isAuxAction && forPlayerId === 0) {
              newSchlangenkorbPlayState.playedCount +=1;
              if (newSchlangenkorbPlayState.playedCount >= newSchlangenkorbPlayState.cardsToPlay) {
                newSchlangenkorbPlayState = { active: false, cardsToPlay: 0, playedCount: 0};
              }
          }
          if (newComebackActive && !isAuxAction && forPlayerId === 0) { 
              newComebackActionsRemaining -=1;
              if (newComebackActionsRemaining <= 0) newComebackActive = false;
          }
  
          newMoves += 1;
          localToastInfo = { title: `${isCurrentPlayerAi ? `Gegner ${forPlayerId}` : 'Du'} spielt Farbkarte: ${cardToPlay.name || cardToPlay.text}`, description: `Auf Schlange ${snakeToPlaceInIdx + 1} gelegt.` };
        } else {
          localToastInfo = { title: "Ungültig", description: "Farbkarten müssen auf eine Schlange gespielt werden.", variant: "destructive" };
          return {...prevGameState, toastInfoForDisplay: localToastInfo, selectedCardForPlay: forPlayerId === 0 ? prevGameState.selectedCardForPlay : null};
        }
      } else if (cardToPlay.type === CARD_TYPES.SPECIAL) {
        debugLog(`[usePlayerActions] SPECIAL card detected: ${cardToPlay.name}, type: '${cardToPlay.type}', CARD_TYPES.SPECIAL: '${CARD_TYPES.SPECIAL}'`);
        const tempGameStateForEffect = { 
            ...prevGameState, 
            playerHand: newPlayerHandState,
            playerSnakes: newPlayerSnakesState,
            aiOpponentsData: isCurrentPlayerAi ? prevGameState.aiOpponentsData.map(opp => opp.id === forPlayerId ? {...opp, hand: newPlayerHandState, snakes: newPlayerSnakesState, activeSnakeIndex: newActiveSnakeIndexToUse, isBlockedByGrube: opp.isBlockedByGrube} : opp) : prevGameState.aiOpponentsData,
            discardPile: newDiscardPile,
            colorCardsPlayedThisTurn: newColorCardsPlayedThisTurn,
            specialCardsPlayedThisTurn: newSpecialCardsPlayedThisTurn,
            moves: newMoves,
            schlangenkorbPlayState: newSchlangenkorbPlayState,
            comebackActive: newComebackActive,
            comebackActionsRemaining: newComebackActionsRemaining,
            mustPlaySpecialCard: newMustPlaySpecialCardState,
            activeSnakeIndex: newActiveSnakeIndexToUse,
            playedSpecialCardsHistory: newPlayedSpecialCardsHistory,
            turnNumber: prevGameState.turnNumber,
            currentPlayerId: forPlayerId, 
        };
        debugLog(`[usePlayerActions] Calling applyCardEffect for ${cardToPlay.name}`);
        effectResult = applyCardEffect(tempGameStateForEffect, cardToPlay, null, isAuxAction);
        
        if (isCurrentPlayerAi) {
            const affectedAiPlayer = effectResult.aiOpponentsData.find(opp => opp.id === forPlayerId);
            if(affectedAiPlayer) {
                newPlayerHandState = affectedAiPlayer.hand;
                newPlayerSnakesState = affectedAiPlayer.snakes;
            }
        } else {
            newPlayerHandState = effectResult.playerHand;
            newPlayerSnakesState = effectResult.playerSnakes;
        }
        
        newDiscardPile = effectResult.discardPile;
        if (!isAuxAction) {
          if (isCurrentPlayerAi) {
            // Für KI: Manuell die Sonderkartenzähler erhöhen
            newSpecialCardsPlayedThisTurn += 1;
          } else {
            // Für menschlichen Spieler: Aus effectResult übernehmen
            newColorCardsPlayedThisTurn = effectResult.colorCardsPlayedThisTurn; 
            newSpecialCardsPlayedThisTurn = effectResult.specialCardsPlayedThisTurn; 
          }
        }
        newMoves = effectResult.moves;
        localToastInfo = effectResult.toastInfo;
        if (forPlayerId === 0) newModalRequired = effectResult.modalRequired; 
        newSchlangenkorbPlayState = effectResult.schlangenkorbPlayState;
        newComebackActive = effectResult.comebackActive;
        newComebackActionsRemaining = effectResult.comebackActionsRemaining;
        newPlayedSpecialCardsHistory = effectResult.playedSpecialCardsHistory;
      } else {
        debugLog(`[usePlayerActions] CARD NOT RECOGNIZED as special: ${cardToPlay.name}, type: '${cardToPlay.type}', expected: '${CARD_TYPES.SPECIAL}'`);
      }
      
      if (newMustPlaySpecialCardState && cardToPlay.type === CARD_TYPES.SPECIAL && !isAuxAction) {
        newMustPlaySpecialCardState = false;
      }
      
      let finalUpdatedState = { ...prevGameState };

      if (isCurrentPlayerAi) {
        if (cardToPlay.type === CARD_TYPES.SPECIAL && effectResult) {
          finalUpdatedState.aiOpponentsData = effectResult.aiOpponentsData.map(opp => 
            opp.id === forPlayerId 
            ? { 
                ...opp, 
                hand: newPlayerHandState, 
                snakes: newPlayerSnakesState, 
                activeSnakeIndex: newActiveSnakeIndexToUse,
                colorCardsPlayedThisTurn: newColorCardsPlayedThisTurn,
                specialCardsPlayedThisTurn: newSpecialCardsPlayedThisTurn,
                mustPlaySpecialCard: newMustPlaySpecialCardState,
              } 
            : opp
          );
          finalUpdatedState.playedSpecialCardsHistory = effectResult.playedSpecialCardsHistory;
        } else {
          finalUpdatedState.aiOpponentsData = prevGameState.aiOpponentsData.map(opp => 
            opp.id === forPlayerId 
            ? { 
                ...opp, 
                hand: newPlayerHandState, 
                snakes: newPlayerSnakesState, 
                activeSnakeIndex: newActiveSnakeIndexToUse,
                colorCardsPlayedThisTurn: newColorCardsPlayedThisTurn,
                specialCardsPlayedThisTurn: newSpecialCardsPlayedThisTurn,
                mustPlaySpecialCard: newMustPlaySpecialCardState,
              } 
            : opp
          );
        }
      } else {
        finalUpdatedState.playerHand = newPlayerHandState;
        finalUpdatedState.playerSnakes = newPlayerSnakesState;
        finalUpdatedState.activeSnakeIndex = newActiveSnakeIndexToUse;
        finalUpdatedState.colorCardsPlayedThisTurn = newColorCardsPlayedThisTurn;
        finalUpdatedState.specialCardsPlayedThisTurn = newSpecialCardsPlayedThisTurn;
        finalUpdatedState.mustPlaySpecialCard = newMustPlaySpecialCardState;
        finalUpdatedState.schlangenkorbPlayState = newSchlangenkorbPlayState;
        finalUpdatedState.comebackActive = newComebackActive;
        finalUpdatedState.comebackActionsRemaining = newComebackActionsRemaining;
        if (cardToPlay.type === CARD_TYPES.SPECIAL && effectResult) {
          finalUpdatedState.aiOpponentsData = effectResult.aiOpponentsData;
        }
      }
      
      finalUpdatedState.discardPile = newDiscardPile;
      finalUpdatedState.selectedCardForPlay = forPlayerId === 0 ? null : prevGameState.selectedCardForPlay; 
      finalUpdatedState.moves = newMoves;
      finalUpdatedState.modalRequired = newModalRequired;
      if (!isCurrentPlayerAi || cardToPlay.type !== CARD_TYPES.SPECIAL) {
        finalUpdatedState.playedSpecialCardsHistory = newPlayedSpecialCardsHistory;
      }
      finalUpdatedState.toastInfoForDisplay = localToastInfo;
      
      return finalUpdatedState;
    });
  }, [setGameState, toast]);

  const handleCardSelectFromHand = (card) => {
    if (!gameState.isPlayerTurn || gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring') {
      setGameState(prev => ({...prev, toastInfoForDisplay: { title: "Aktion nicht möglich", description: "Das Spiel ist beendet oder du bist nicht am Zug.", variant: "destructive" }}));
      return;
    }
    setGameState(prev => ({ ...prev, selectedCardForPlay: card.id === prev.selectedCardForPlay?.id ? null : card }));
  };
  
  const handleDragStartFromHand = (card) => {
     setGameState(prev => ({ ...prev, selectedCardForPlay: card }));
  }

  const handleDropOnSnake = (draggedCardInfo, zoneName, snakeIndex, position) => {
    if (!draggedCardInfo || !draggedCardInfo.id) {
      setGameState(prev => ({...prev, toastInfoForDisplay: { title: "Drag & Drop Fehler", description: "Ungültiges Kartenobjekt gezogen.", variant: "destructive" }}));
      return;
    }
    playSelectedCardLogic(draggedCardInfo, 'Schlangenzone', position, false, snakeIndex, 0);
  };

  const handleClickOnSnakePosition = (snakeIndex, position) => {
    if (gameState.selectedCardForPlay) {
      playSelectedCardLogic(gameState.selectedCardForPlay, 'Schlangenzone', position, false, snakeIndex, 0);
    } else {
      setGameState(prev => ({...prev, toastInfoForDisplay: { title: "Keine Karte ausgewählt", description: "Bitte wähle zuerst eine Karte aus deiner Hand aus.", variant: "destructive" }}));
    }
  };
  
  const handleDropOnDiscard = (item) => {
    const cardInHand = gameState.playerHand.find(c => c && c.id === item.id);
    if (!cardInHand) {
      setGameState(prev => ({...prev, toastInfoForDisplay: { title: "Fehler", description: "Diese Karte ist nicht in deiner Hand.", variant: "destructive" }}));
      return;
    }
    setGameState(prev => ({...prev, toastInfoForDisplay: { title: "Aktion nicht erlaubt", description: "Karten können nicht direkt auf den Ablagestapel gezogen werden.", variant: "destructive" }}));
  };

  const handleCompleteTask = (taskId) => {
    if (gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring') return;
    setGameState(prev => {
        const newState = checkTaskCompletionLocal(prev, taskId, toast); 
        return {...newState, toastInfoForDisplay: newState.toastInfo}; 
    });
  };

  const endTurn = () => {
    if (gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring') return;
    if (!gameState.isPlayerTurn) {
      setGameState(prev => ({...prev, toastInfoForDisplay: { title: "Nicht am Zug", description: "Du kannst deinen Zug nicht beenden.", variant: "destructive" }}));
      return;
    }
    setGameState(prev => {
        const newState = handleEndTurn(prev, toast, () => {}, 0); 
        return {...newState, toastInfoForDisplay: newState.toastInfo}; 
    });
  };

  const handleModalSubmit = (modalType, payload) => {
    const resultState = handleModalAction(gameState, modalType, payload, (card, zone, pos, aux) => playSelectedCardLogic(card, zone, pos, aux, null, 0));
    setGameState(prev => ({...prev, ...resultState, toastInfoForDisplay: resultState.toastInfo}));
  };

  const handleStartNewSnake = () => {
    if (gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring') return;
    if (gameState.playerSnakes.length < 2) {
        setGameState(prev => {
            const newActiveIndex = prev.playerSnakes.length;
            return {
                ...prev,
                playerSnakes: [...prev.playerSnakes, []],
                activeSnakeIndex: newActiveIndex,
                toastInfoForDisplay: {title: "Neue Schlange begonnen", description: "Du kannst jetzt Karten an deine zweite Schlange anlegen."}
            }
        });
    } else {
        setGameState(prev => ({...prev, toastInfoForDisplay: {title: "Maximum erreicht", description: "Du darfst maximal 2 Schlangen haben.", variant: "destructive"}}));
    }
  };

  const handleActivateComeback = () => {
    if (gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring') return;
    if (canActivateComebackCard(gameState) && !gameState.auxCards.comebackCard.used) {
        setGameState(prev => ({
            ...prev,
            modalRequired: { type: 'COMEBACK_ACTIONS', cardName: 'Comeback-Karte' }
        }));
    } else {
        setGameState(prev => ({...prev, toastInfoForDisplay: {title: "Bedingung nicht erfüllt", description: "Comeback-Karte kann nicht aktiviert werden.", variant: "destructive"}}));
    }
  };

  const handleActivateRiskReward = () => {
    if (gameState.gamePhase === 'gameOver' || gameState.gamePhase === 'scoring') return;
    if (gameState.auxCards.riskRewardCards.some(c => !c.used)) {
        setGameState(prev => ({
            ...prev,
            modalRequired: { type: 'RISK_REWARD_ACTIONS', cardName: 'Risiko-Belohnung' }
        }));
    } else {
        setGameState(prev => ({...prev, toastInfoForDisplay: {title: "Keine Karten", description: "Keine Risiko-Belohnungs-Karten mehr verfügbar.", variant: "destructive"}}));
    }
  };

  return {
    playSelectedCardLogic,
    handleCardSelectFromHand,
    handleDragStartFromHand,
    handleDropOnSnake,
    handleClickOnSnakePosition,
    handleDropOnDiscard,
    handleCompleteTask,
    endTurn,
    handleModalSubmit,
    handleStartNewSnake,
    handleActivateComeback,
    handleActivateRiskReward,
  };
};
