import React, { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Trophy, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const Leaderboard = () => {
  const [scores, setScores] = useState([]);

  useEffect(() => {
    const fetchScores = () => {
      try {
        const storedScores = localStorage.getItem('cardSnakeScores');
        if (storedScores) {
          const parsedScores = JSON.parse(storedScores);
          const topScores = parsedScores
            .sort((a, b) => b.points - a.points)
            .slice(0, 10);
          setScores(topScores);
        } else {
          setScores([
            { name: "<PERSON><PERSON> <PERSON><PERSON>", points: 1500, date: new Date().toLocaleDateString() },
            { name: "<PERSON><PERSON><PERSON>", points: 1250, date: new Date().toLocaleDateString() },
            { name: "Strategie Fuchs", points: 1100, date: new Date().toLocaleDateString() },
            { name: "Glückspilz", points: 950, date: new Date().toLocaleDateString() },
            { name: "Anfänger Pro", points: 800, date: new Date().toLocaleDateString() },
          ]);
        }
      } catch (error) {
        console.error("Fehler beim Laden der Bestenliste:", error);
        setScores([
          { name: "Error Player", points: 0, date: new Date().toLocaleDateString() },
        ]);
      }
    };
    fetchScores();
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-800 to-slate-600 p-4">
      <Card className="w-full max-w-2xl bg-slate-700 border-slate-600 text-white shadow-2xl">
        <CardHeader className="text-center">
          <Trophy className="mx-auto h-12 w-12 text-yellow-400 mb-4" />
          <CardTitle className="text-3xl font-bold text-yellow-400">Bestenliste</CardTitle>
          <CardDescription className="text-slate-300">Die Top 10 Spieler von Card Snake.</CardDescription>
        </CardHeader>
        <CardContent>
          {scores.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow className="border-slate-500 hover:bg-slate-600/50">
                  <TableHead className="w-[100px] text-yellow-400 font-semibold">Rang</TableHead>
                  <TableHead className="text-yellow-400 font-semibold">Name</TableHead>
                  <TableHead className="text-yellow-400 font-semibold">Punkte</TableHead>
                  <TableHead className="text-right text-yellow-400 font-semibold">Datum</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {scores.map((score, index) => (
                  <TableRow key={index} className="border-slate-500 hover:bg-slate-600/50">
                    <TableCell className="font-medium text-slate-200">{index + 1}</TableCell>
                    <TableCell className="text-slate-200">{score.name}</TableCell>
                    <TableCell className="text-slate-200">{score.points}</TableCell>
                    <TableCell className="text-right text-slate-300 text-sm">{score.date}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <p className="text-center text-slate-400 py-8">Noch keine Einträge in der Bestenliste vorhanden.</p>
          )}
        </CardContent>
      </Card>
      <Button asChild variant="outline" className="mt-8 text-purple-400 border-purple-400 hover:bg-purple-400 hover:text-white">
        <Link to="/">
          <ArrowLeft className="mr-2 h-5 w-5" />
          Zurück zum Hauptmenü
        </Link>
      </Button>
    </div>
  );
};

export default Leaderboard;