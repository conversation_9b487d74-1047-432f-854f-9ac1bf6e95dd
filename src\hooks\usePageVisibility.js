import { useEffect, useRef } from 'react';
import { debugLog, debugWarn } from '@/utils/debugUtils.jsx';

/**
 * Hook zur Überwachung der Page Visibility und Verhinderung von automatischem Spielende
 * @param {Object} gameState - Aktueller Spielzustand
 * @param {Function} setGameState - State-Setter
 * @returns {Object} - Visibility-Status und Kontrollfunktionen
 */
export const usePageVisibility = (gameState, setGameState) => {
  const isVisibleRef = useRef(true);
  const lastVisibilityChangeRef = useRef(Date.now());
  const gameStateBackupRef = useRef(null);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      const now = Date.now();
      const timeSinceLastChange = now - lastVisibilityChangeRef.current;

      // Nur bei wichtigen Änderungen loggen
      if (timeSinceLastChange > 1000) { // Nur bei Änderungen > 1 Sekunde
        debugLog(`[PageVisibility] Visibility changed: ${isVisible ? 'VISIBLE' : 'HIDDEN'}, Game Phase: ${gameState?.gamePhase}`);
      }

      if (!isVisible) {
        // Seite wird versteckt - Backup erstellen
        if (gameState && gameState.gamePhase === 'playing') {
          gameStateBackupRef.current = {
            ...gameState,
            backupTimestamp: now,
            backupReason: 'PAGE_HIDDEN'
          };
          debugLog('[PageVisibility] Game state backed up before hiding - Phase:', gameState.gamePhase);

          // Verhindere automatische Timeouts
          debugLog('[PageVisibility] Preventing automatic game end due to page hide');
        }
      } else {
        // Seite wird wieder sichtbar
        debugLog('[PageVisibility] Page became visible, checking for game restoration...');

        if (gameStateBackupRef.current) {
          const backupAge = now - (gameStateBackupRef.current.backupTimestamp || 0);
          debugLog(`[PageVisibility] Backup found, age: ${backupAge}ms, backup phase: ${gameStateBackupRef.current.gamePhase}, current phase: ${gameState?.gamePhase}`);

          // Prüfe ob Spiel ungewollt beendet wurde
          if (gameStateBackupRef.current.gamePhase === 'playing' &&
              gameState?.gamePhase !== 'playing' &&
              backupAge < 30000) { // Nur innerhalb von 30 Sekunden wiederherstellen

            debugWarn('[PageVisibility] Game was ended while page was hidden - attempting restore');

            // Versuche Spielzustand wiederherzustellen
            const restoredState = { ...gameStateBackupRef.current };
            delete restoredState.backupTimestamp;
            delete restoredState.backupReason;

            setGameState(prev => ({
              ...restoredState,
              toastInfoForDisplay: {
                title: "Spiel wiederhergestellt",
                description: "Das Spiel wurde nach Fensterwechsel wiederhergestellt",
                duration: 3000
              }
            }));

            debugLog('[PageVisibility] Game state restored successfully');
          } else if (gameState?.gamePhase === 'playing') {
            debugLog('[PageVisibility] Game is still running, no restoration needed');
          }
        }
      }

      isVisibleRef.current = isVisible;
      lastVisibilityChangeRef.current = now;
    };

    const handleFocus = () => {
      // Nur loggen wenn Spiel läuft
      if (gameState?.gamePhase === 'playing') {
        debugLog('[PageVisibility] Window focused during game');
      }
      isVisibleRef.current = true;
    };

    const handleBlur = () => {
      // Nur loggen wenn Spiel läuft
      if (gameState?.gamePhase === 'playing') {
        debugLog('[PageVisibility] Window blurred during game');
      }
      // Nicht automatisch als unsichtbar markieren, da Blur nicht immer Verstecken bedeutet
    };

    const handleBeforeUnload = (event) => {
      if (gameState && gameState.gamePhase === 'playing') {
        debugWarn('[PageVisibility] Page unload detected during active game');
        // Erstelle Backup vor dem Verlassen
        if (gameStateBackupRef.current) {
          localStorage.setItem('schlangentanz_game_backup', JSON.stringify({
            gameState: gameStateBackupRef.current,
            timestamp: Date.now(),
            reason: 'PAGE_UNLOAD'
          }));
          debugLog('[PageVisibility] Game backup saved to localStorage before unload');
        }
        // Warnung anzeigen (Browser-abhängig)
        event.preventDefault();
        event.returnValue = 'Das Spiel läuft noch. Möchten Sie wirklich die Seite verlassen?';
        return event.returnValue;
      }
    };

    const handlePageShow = (event) => {
      debugLog('[PageVisibility] Page show event - checking for game restoration');

      // Prüfe auf gespeichertes Backup
      try {
        const backup = localStorage.getItem('schlangentanz_game_backup');
        if (backup) {
          const { gameState: backupState, timestamp, reason } = JSON.parse(backup);
          const backupAge = Date.now() - timestamp;

          if (backupAge < 60000 && backupState.gamePhase === 'playing') { // 1 Minute
            debugLog('[PageVisibility] Found recent game backup, attempting restore');
            setGameState(prev => ({
              ...backupState,
              toastInfoForDisplay: {
                title: "Spiel wiederhergestellt",
                description: `Spiel nach ${reason} wiederhergestellt`,
                duration: 4000
              }
            }));
            localStorage.removeItem('schlangentanz_game_backup');
          }
        }
      } catch (error) {
        debugWarn('[PageVisibility] Error restoring game backup:', error);
      }
    };

    // Event Listeners hinzufügen
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pageshow', handlePageShow);

    debugLog('[PageVisibility] Event listeners registered');

    // Prüfe beim ersten Laden auf Backup
    handlePageShow();

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('pageshow', handlePageShow);
      debugLog('[PageVisibility] Event listeners removed');
    };
  }, [gameState, setGameState]);

  return {
    isVisible: isVisibleRef.current,
    lastVisibilityChange: lastVisibilityChangeRef.current,
    hasBackup: !!gameStateBackupRef.current
  };
};

/**
 * Hook zur Verhinderung von automatischen Timeouts beim Fensterwechsel
 * @param {Function} callback - Callback-Funktion die aufgerufen werden soll
 * @param {number} delay - Verzögerung in Millisekunden
 * @param {Array} dependencies - Abhängigkeiten für useEffect
 * @returns {Function} - Cleanup-Funktion
 */
export const useVisibilityAwareTimeout = (callback, delay, dependencies = []) => {
  const timeoutRef = useRef(null);
  const startTimeRef = useRef(null);
  const remainingTimeRef = useRef(delay);

  useEffect(() => {
    const startTimeout = () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      startTimeRef.current = Date.now();
      remainingTimeRef.current = delay;

      timeoutRef.current = setTimeout(() => {
        if (!document.hidden) {
          callback();
        } else {
          debugLog('[VisibilityAwareTimeout] Callback skipped - page is hidden');
        }
      }, delay);
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Seite versteckt - pausiere Timer
        if (timeoutRef.current && startTimeRef.current) {
          const elapsed = Date.now() - startTimeRef.current;
          remainingTimeRef.current = Math.max(0, delay - elapsed);
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
          debugLog(`[VisibilityAwareTimeout] Timer paused, remaining: ${remainingTimeRef.current}ms`);
        }
      } else {
        // Seite sichtbar - setze Timer fort
        if (!timeoutRef.current && remainingTimeRef.current > 0) {
          startTimeRef.current = Date.now();
          timeoutRef.current = setTimeout(() => {
            callback();
          }, remainingTimeRef.current);
          debugLog(`[VisibilityAwareTimeout] Timer resumed with ${remainingTimeRef.current}ms`);
        }
      }
    };

    // Starte initialen Timer
    startTimeout();

    // Überwache Visibility Changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, dependencies);

  return () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };
};

/**
 * Hook zur Überwachung von Session Storage Änderungen (für AI Turn Tracking)
 * @param {string} key - Session Storage Key
 * @param {Function} onInvalidValue - Callback bei ungültigen Werten
 */
export const useSessionStorageMonitor = (key, onInvalidValue) => {
  useEffect(() => {
    const checkSessionStorage = () => {
      try {
        const value = sessionStorage.getItem(key);
        if (value) {
          const numValue = parseInt(value);
          if (numValue > 10) { // Verdächtig hoher Wert
            debugWarn(`[SessionStorage] Suspicious value for ${key}: ${numValue}`);
            if (onInvalidValue) {
              onInvalidValue(key, numValue);
            }
          }
        }
      } catch (error) {
        debugWarn(`[SessionStorage] Error checking ${key}:`, error);
      }
    };

    // Prüfe alle 5 Sekunden
    const interval = setInterval(checkSessionStorage, 5000);

    return () => clearInterval(interval);
  }, [key, onInvalidValue]);
};
