import { useEffect, useRef } from 'react';
import { debugLog, debugWarn } from '@/utils/debugUtils.jsx';

/**
 * Hook zur Überwachung der Page Visibility und Verhinderung von automatischem Spielende
 * @param {Object} gameState - Aktueller Spielzustand
 * @param {Function} setGameState - State-Setter
 * @returns {Object} - Visibility-Status und Kontrollfunktionen
 */
export const usePageVisibility = (gameState, setGameState) => {
  const isVisibleRef = useRef(true);
  const lastVisibilityChangeRef = useRef(Date.now());
  const gameStateBackupRef = useRef(null);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      const now = Date.now();
      const timeSinceLastChange = now - lastVisibilityChangeRef.current;

      debugLog(`[PageVisibility] Visibility changed: ${isVisible ? 'VISIBLE' : 'HIDDEN'}, Time since last: ${timeSinceLastChange}ms`);

      if (!isVisible) {
        // Seite wird versteckt - Backup erstellen
        if (gameState && gameState.gamePhase === 'playing') {
          gameStateBackupRef.current = { ...gameState };
          debugLog('[PageVisibility] Game state backed up before hiding');
        }
      } else {
        // Seite wird wieder sichtbar
        if (timeSinceLastChange > 1000) { // Nur bei längeren Unterbrechungen
          debugLog('[PageVisibility] Page became visible after significant time');
          
          // Prüfe ob Spiel ungewollt beendet wurde
          if (gameStateBackupRef.current && 
              gameStateBackupRef.current.gamePhase === 'playing' && 
              gameState.gamePhase !== 'playing') {
            
            debugWarn('[PageVisibility] Game was ended while page was hidden - attempting restore');
            
            // Versuche Spielzustand wiederherzustellen
            setGameState(prev => ({
              ...gameStateBackupRef.current,
              toastInfoForDisplay: {
                title: "Spiel wiederhergestellt",
                description: "Das Spiel wurde nach Fensterwechsel wiederhergestellt",
                duration: 3000
              }
            }));
          }
        }
      }

      isVisibleRef.current = isVisible;
      lastVisibilityChangeRef.current = now;
    };

    const handleFocus = () => {
      debugLog('[PageVisibility] Window focused');
      isVisibleRef.current = true;
    };

    const handleBlur = () => {
      debugLog('[PageVisibility] Window blurred');
      // Nicht automatisch als unsichtbar markieren, da Blur nicht immer Verstecken bedeutet
    };

    const handleBeforeUnload = (event) => {
      if (gameState && gameState.gamePhase === 'playing') {
        debugWarn('[PageVisibility] Page unload detected during active game');
        // Warnung anzeigen (Browser-abhängig)
        event.preventDefault();
        event.returnValue = 'Das Spiel läuft noch. Möchten Sie wirklich die Seite verlassen?';
        return event.returnValue;
      }
    };

    // Event Listeners hinzufügen
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);
    window.addEventListener('beforeunload', handleBeforeUnload);

    debugLog('[PageVisibility] Event listeners registered');

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      debugLog('[PageVisibility] Event listeners removed');
    };
  }, [gameState, setGameState]);

  return {
    isVisible: isVisibleRef.current,
    lastVisibilityChange: lastVisibilityChangeRef.current,
    hasBackup: !!gameStateBackupRef.current
  };
};

/**
 * Hook zur Verhinderung von automatischen Timeouts beim Fensterwechsel
 * @param {Function} callback - Callback-Funktion die aufgerufen werden soll
 * @param {number} delay - Verzögerung in Millisekunden
 * @param {Array} dependencies - Abhängigkeiten für useEffect
 * @returns {Function} - Cleanup-Funktion
 */
export const useVisibilityAwareTimeout = (callback, delay, dependencies = []) => {
  const timeoutRef = useRef(null);
  const startTimeRef = useRef(null);
  const remainingTimeRef = useRef(delay);

  useEffect(() => {
    const startTimeout = () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      startTimeRef.current = Date.now();
      remainingTimeRef.current = delay;

      timeoutRef.current = setTimeout(() => {
        if (!document.hidden) {
          callback();
        } else {
          debugLog('[VisibilityAwareTimeout] Callback skipped - page is hidden');
        }
      }, delay);
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Seite versteckt - pausiere Timer
        if (timeoutRef.current && startTimeRef.current) {
          const elapsed = Date.now() - startTimeRef.current;
          remainingTimeRef.current = Math.max(0, delay - elapsed);
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
          debugLog(`[VisibilityAwareTimeout] Timer paused, remaining: ${remainingTimeRef.current}ms`);
        }
      } else {
        // Seite sichtbar - setze Timer fort
        if (!timeoutRef.current && remainingTimeRef.current > 0) {
          startTimeRef.current = Date.now();
          timeoutRef.current = setTimeout(() => {
            callback();
          }, remainingTimeRef.current);
          debugLog(`[VisibilityAwareTimeout] Timer resumed with ${remainingTimeRef.current}ms`);
        }
      }
    };

    // Starte initialen Timer
    startTimeout();

    // Überwache Visibility Changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, dependencies);

  return () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };
};

/**
 * Hook zur Überwachung von Session Storage Änderungen (für AI Turn Tracking)
 * @param {string} key - Session Storage Key
 * @param {Function} onInvalidValue - Callback bei ungültigen Werten
 */
export const useSessionStorageMonitor = (key, onInvalidValue) => {
  useEffect(() => {
    const checkSessionStorage = () => {
      try {
        const value = sessionStorage.getItem(key);
        if (value) {
          const numValue = parseInt(value);
          if (numValue > 10) { // Verdächtig hoher Wert
            debugWarn(`[SessionStorage] Suspicious value for ${key}: ${numValue}`);
            if (onInvalidValue) {
              onInvalidValue(key, numValue);
            }
          }
        }
      } catch (error) {
        debugWarn(`[SessionStorage] Error checking ${key}:`, error);
      }
    };

    // Prüfe alle 5 Sekunden
    const interval = setInterval(checkSessionStorage, 5000);

    return () => clearInterval(interval);
  }, [key, onInvalidValue]);
};
