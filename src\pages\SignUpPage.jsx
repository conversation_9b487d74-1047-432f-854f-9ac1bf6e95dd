import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';
import { UserPlus, Mail, Lock, User as UserIcon } from 'lucide-react';

const SignUpPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { signUp } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (password !== confirmPassword) {
      setError("Passwörter stimmen nicht überein.");
      toast({ title: "Fehler", description: "Die eingegebenen Passwörter stimmen nicht überein.", variant: "destructive" });
      return;
    }
    if (password.length < 6) {
        setError("Das Passwort muss mindestens 6 Zeichen lang sein.");
        toast({ title: "Fehler", description: "Das Passwort muss mindestens 6 Zeichen lang sein.", variant: "destructive" });
        return;
    }
    setError('');
    setLoading(true);
    // session wird hier benötigt, um zu prüfen, ob der User direkt eingeloggt wurde
    const { error: signUpError, user, session } = await signUp(email, password, username);
    setLoading(false);

    if (signUpError) {
      setError(signUpError.message || "Registrierung fehlgeschlagen.");
      toast({ title: "Registrierungsfehler", description: signUpError.message || "Bitte versuche es später erneut.", variant: "destructive" });
    } else {
        // Wenn session vorhanden ist, bedeutet das, der User wurde eingeloggt (z.B. Email Confirmation ist aus)
        // Die Navigation zu /main-menu wird dann vom AuthContext (onAuthStateChange) gehandhabt.
        if (user && session) { 
            toast({ title: "Registrierung erfolgreich!", description: "Dein Konto wurde erstellt und du wurdest angemeldet." });
            // Keine explizite Navigation hier, AuthContext übernimmt
        } else if (user) { // User wurde erstellt, aber session ist null (Email Confirmation ist an)
            toast({ title: "Registrierung initiiert!", description: "Bitte überprüfe deine E-Mails, um deine Registrierung abzuschließen." });
            navigate('/signin'); 
        } else {
            // Fallback, sollte nicht oft eintreten
            setError("Registrierung fehlgeschlagen. Bitte versuche es erneut.");
            toast({ title: "Registrierungsfehler", description: "Ein unbekannter Fehler ist aufgetreten.", variant: "destructive" });
        }
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-900 via-primary-dark-green to-slate-800 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="w-full max-w-md bg-slate-800 text-slate-100 border-primary-gold shadow-xl">
          <CardHeader className="text-center">
            <img-replace src="/logo.png" alt="ColorConda Logo" className="w-24 h-24 mx-auto mb-6 rounded-full shadow-lg border-2 border-primary-gold p-1"/>
            <CardTitle className="text-3xl font-heading text-primary-gold">Konto erstellen</CardTitle>
            <CardDescription className="text-slate-400">Tritt ColorConda bei und beginne dein Schlangenabenteuer!</CardDescription>
          </CardHeader>
          <CardContent>
            {error && <p className="text-red-500 text-sm bg-red-900/30 p-3 rounded-md mb-4 text-center">{error}</p>}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="username" className="text-slate-300">Benutzername (optional)</Label>
                 <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                    <Input 
                        id="username" 
                        type="text" 
                        value={username} 
                        onChange={(e) => setUsername(e.target.value)} 
                        className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                        placeholder="Dein Spielername"
                    />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-slate-300">E-Mail</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="email" 
                    type="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)} 
                    required 
                    className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password" className="text-slate-300">Passwort</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="password" 
                    type="password" 
                    value={password} 
                    onChange={(e) => setPassword(e.target.value)} 
                    required 
                    minLength="6"
                    className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                    placeholder="Mindestens 6 Zeichen"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-slate-300">Passwort bestätigen</Label>
                 <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                  <Input 
                    id="confirmPassword" 
                    type="password" 
                    value={confirmPassword} 
                    onChange={(e) => setConfirmPassword(e.target.value)} 
                    required 
                    className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                    placeholder="Passwort wiederholen"
                  />
                </div>
              </div>
              <Button type="submit" className="w-full bg-primary-gold hover:bg-primary-gold/80 text-slate-900 font-semibold py-3 text-lg" disabled={loading}>
                <UserPlus className="mr-2 h-5 w-5" /> {loading ? 'Registriere...' : 'Registrieren'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col items-center space-y-3 pt-6">
            <p className="text-sm text-slate-400">
              Bereits ein Konto? <Link to="/signin" className="text-primary-gold hover:underline">Anmelden</Link>
            </p>
            <Link to="/" className="text-xs text-slate-500 hover:text-primary-gold transition-colors">
              Zurück zur Startseite
            </Link>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default SignUpPage;