// File-basiertes Logging-System für Server-seitige Logs
import fs from 'fs';
import path from 'path';

const LOG_DIR = 'logs';
const LOG_FILE = 'debug.log';
const LOG_PATH = path.join(process.cwd(), LOG_DIR, LOG_FILE);

// <PERSON><PERSON> sicher, dass der Log-Ordner existiert
const ensureLogDir = () => {
  try {
    if (!fs.existsSync(path.join(process.cwd(), LOG_DIR))) {
      fs.mkdirSync(path.join(process.cwd(), LOG_DIR), { recursive: true });
    }
  } catch (error) {
    console.error('Fehler beim Erstellen des Log-Ordners:', error);
  }
};

// Formatiere Log-Eintrag
const formatLogEntry = (level, message, ...args) => {
  const timestamp = new Date().toISOString();
  const argsStr = args.length > 0 ? ' ' + args.map(arg => 
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
  ).join(' ') : '';
  return `[${timestamp}] [${level}] ${message}${argsStr}\n`;
};

// Schreibe in Log-Datei
const writeToLogFile = (logEntry) => {
  try {
    ensureLogDir();
    fs.appendFileSync(LOG_PATH, logEntry, 'utf8');
  } catch (error) {
    console.error('Fehler beim Schreiben in Log-Datei:', error);
  }
};

// Exportierte Log-Funktionen
export const fileLog = (message, ...args) => {
  const logEntry = formatLogEntry('INFO', message, ...args);
  writeToLogFile(logEntry);
  console.log(message, ...args); // Auch in Konsole
};

export const fileWarn = (message, ...args) => {
  const logEntry = formatLogEntry('WARN', message, ...args);
  writeToLogFile(logEntry);
  console.warn(message, ...args);
};

export const fileError = (message, ...args) => {
  const logEntry = formatLogEntry('ERROR', message, ...args);
  writeToLogFile(logEntry);
  console.error(message, ...args);
};

// Lese Log-Datei
export const readLogFile = () => {
  try {
    if (fs.existsSync(LOG_PATH)) {
      return fs.readFileSync(LOG_PATH, 'utf8');
    }
    return '';
  } catch (error) {
    console.error('Fehler beim Lesen der Log-Datei:', error);
    return '';
  }
};

// Lösche Log-Datei
export const clearLogFile = () => {
  try {
    if (fs.existsSync(LOG_PATH)) {
      fs.writeFileSync(LOG_PATH, '', 'utf8');
      console.log('Log-Datei geleert');
    }
  } catch (error) {
    console.error('Fehler beim Löschen der Log-Datei:', error);
  }
};

// Log-Datei-Statistiken
export const getLogFileStats = () => {
  try {
    if (fs.existsSync(LOG_PATH)) {
      const stats = fs.statSync(LOG_PATH);
      const content = fs.readFileSync(LOG_PATH, 'utf8');
      const lines = content.split('\n').filter(line => line.trim() !== '');
      
      return {
        exists: true,
        size: stats.size,
        lines: lines.length,
        lastModified: stats.mtime,
        path: LOG_PATH
      };
    }
    return { exists: false };
  } catch (error) {
    console.error('Fehler beim Abrufen der Log-Statistiken:', error);
    return { exists: false, error: error.message };
  }
};
