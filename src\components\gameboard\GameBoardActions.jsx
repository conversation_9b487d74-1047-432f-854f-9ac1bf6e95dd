import React from 'react';
import { motion } from 'framer-motion';
import { Alert<PERSON>riangle, Zap, ShieldCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';

const GameBoardActions = ({ 
  mustPlaySpecialCard, 
  canActivateComeback, 
  onActivateComeback,
  canActivateRiskReward,
  onActivateRiskReward,
  comebackCardUsed,
  riskRewardCardsRemaining
}) => {
  return (
    <div className="absolute top-16 sm:top-20 left-1/2 -translate-x-1/2 z-30 flex flex-col items-center space-y-2">
      {mustPlaySpecialCard && (
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-2 bg-red-600/80 text-white rounded-md shadow-lg text-xs sm:text-sm flex items-center"
        >
          <AlertTriangle className="h-4 w-4 mr-2"/> Du musst eine Sonderkarte spielen!
        </motion.div>
      )}
      <div className="flex space-x-2">
        {canActivateComeback && !comebackCardUsed && (
          <Button onClick={onActivateComeback} variant="outline" size="sm" className="text-yellow-300 border-yellow-500 hover:bg-yellow-500/20">
            <Zap className="h-4 w-4 mr-1" /> Comeback
          </Button>
        )}
        {riskRewardCardsRemaining > 0 && (
           <Button onClick={onActivateRiskReward} variant="outline" size="sm" className="text-purple-300 border-purple-500 hover:bg-purple-500/20">
            <ShieldCheck className="h-4 w-4 mr-1" /> Risiko ({riskRewardCardsRemaining})
          </Button>
        )}
      </div>
    </div>
  );
};

export default GameBoardActions;