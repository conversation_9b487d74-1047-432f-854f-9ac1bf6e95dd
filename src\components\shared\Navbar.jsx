import React from 'react';
import { Link } from 'react-router-dom';
import { Home } from 'lucide-react';

const Navbar = () => {
  return (
    <nav className="bg-secondary text-secondary-foreground shadow-md">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <Link to="/" className="flex items-center space-x-2 text-primary hover:text-primary/80 transition-colors">
          <Home className="h-6 w-6" />
          <span className="font-semibold text-lg">Startseite</span>
        </Link>
        {/* Placeholder für weitere Links */}
      </div>
    </nav>
  );
};

export default Navbar;