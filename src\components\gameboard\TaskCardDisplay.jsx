import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Award, ShieldCheck } from 'lucide-react';
import { CARD_TYPES } from '@/utils/gameRules';

const TaskCardDisplay = ({ task, onCompleteTask, snakeForCheck }) => {
  const Icon = task.isCompleted ? ShieldCheck : Award;
  return (
    <motion.div 
      className={`p-3 rounded-lg shadow-md text-sm ${task.isCompleted ? 'bg-green-700/80' : 'bg-primary-burgundy/70'} border ${task.isCompleted ? 'border-green-500' : 'border-primary-gold/50'}`}
      whileHover={{ scale: task.isCompleted ? 1 : 1.03 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <div className="flex items-center mb-1">
        <Icon className={`mr-2 h-5 w-5 ${task.isCompleted ? 'text-green-300' : 'text-yellow-300'}`} />
        <h4 className="font-semibold font-heading text-base">{task.type === CARD_TYPES.TASK_OPEN ? "Offene Aufgabe" : "Geheime Aufgabe"}</h4>
      </div>
      <p className="text-xs text-slate-200 mb-2">{task.description}</p>
      <p className="text-right font-bold text-yellow-400">{task.points} P.</p>
      {!task.isCompleted && (
        <Button 
            size="sm" 
            className="w-full mt-2 bg-primary-gold/80 hover:bg-primary-gold text-slate-900 text-xs" 
            onClick={() => onCompleteTask(task.id)}
        >
          ColorConda!
        </Button>
      )}
    </motion.div>
  );
};

export default TaskCardDisplay;