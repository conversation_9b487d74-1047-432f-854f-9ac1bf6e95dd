import { CARD_TYPES, SPECIAL_CARDS_NAMES, checkTaskCompletion, createTasks, COLORS } from '@/utils/gameRules';
import { debugLog, debugWarn, debugError } from '@/utils/debugUtils.jsx';

// Intelligente Zielauswahl für Schlangengrube
const selectSchlangengrubeTarget = (gameState, currentAiPlayerId) => {
  debugLog(`[DEBUG][selectSchlangengrubeTarget] KI ${currentAiPlayerId} wählt Schlangengrube-Ziel`);

  // Sam<PERSON>le alle möglichen Ziele (außer dem aktuellen KI-Spieler)
  const allPlayers = [
    {
      id: 0,
      isHuman: true,
      isProtected: gameState.playerProtected,
      snakes: gameState.playerSnakes || [],
      tasks: gameState.playerTasks || [],
      points: calculateCurrentPlayerPoints(gameState.playerSnakes || [], gameState.playerTasks || [])
    },
    ...gameState.aiOpponentsData
      .filter(opp => opp.id !== currentAiPlayerId)
      .map(opp => ({
        id: opp.id,
        isHuman: false,
        isProtected: opp.isProtected || false,
        snakes: opp.snakes || [],
        tasks: opp.tasks || [],
        points: calculateCurrentPlayerPoints(opp.snakes || [], opp.tasks || [])
      }))
  ];

  // Filtere geschützte Spieler heraus
  const validTargets = allPlayers.filter(player => !player.isProtected);

  if (validTargets.length === 0) {
    debugLog(`[DEBUG][selectSchlangengrubeTarget] Keine gültigen Ziele (alle geschützt)`);
    return { id: 0, reason: "Alle Spieler geschützt" };
  }

  debugLog(`[DEBUG][selectSchlangengrubeTarget] Gültige Ziele:`, validTargets.map(p => ({
    id: p.id,
    points: p.points,
    longestSnake: Math.max(0, ...p.snakes.map(s => s.length)),
    tasks: p.tasks.length
  })));

  // Strategie 1: Blockiere den Spieler mit den meisten Punkten
  const maxPoints = Math.max(...validTargets.map(p => p.points));
  const leadingPlayers = validTargets.filter(p => p.points === maxPoints);

  if (leadingPlayers.length === 1) {
    const target = leadingPlayers[0];
    debugLog(`[DEBUG][selectSchlangengrubeTarget] Ziel gewählt: Spieler ${target.id} (Führend mit ${target.points} Punkten)`);
    return {
      id: target.id,
      reason: `Führend mit ${target.points} Punkten`
    };
  }

  // Strategie 2: Bei Punktegleichstand - längste Schlange
  if (leadingPlayers.length > 1) {
    const playersWithLongestSnake = leadingPlayers.map(p => ({
      ...p,
      longestSnake: Math.max(0, ...p.snakes.map(s => s.length))
    }));

    const maxSnakeLength = Math.max(...playersWithLongestSnake.map(p => p.longestSnake));
    const snakeLeaders = playersWithLongestSnake.filter(p => p.longestSnake === maxSnakeLength);

    if (snakeLeaders.length === 1) {
      const target = snakeLeaders[0];
      debugLog(`[DEBUG][selectSchlangengrubeTarget] Ziel gewählt: Spieler ${target.id} (Längste Schlange: ${target.longestSnake})`);
      return {
        id: target.id,
        reason: `Längste Schlange (${target.longestSnake} Karten)`
      };
    }

    // Strategie 3: Bei weiterem Gleichstand - meiste Aufgaben
    const playersWithMostTasks = snakeLeaders.map(p => ({
      ...p,
      taskCount: p.tasks.length
    }));

    const maxTasks = Math.max(...playersWithMostTasks.map(p => p.taskCount));
    const taskLeaders = playersWithMostTasks.filter(p => p.taskCount === maxTasks);

    if (taskLeaders.length === 1) {
      const target = taskLeaders[0];
      debugLog(`[DEBUG][selectSchlangengrubeTarget] Ziel gewählt: Spieler ${target.id} (Meiste Aufgaben: ${target.taskCount})`);
      return {
        id: target.id,
        reason: `Meiste Aufgaben (${target.taskCount})`
      };
    }

    // Strategie 4: Zufallsauswahl bei komplettem Gleichstand
    const randomTarget = taskLeaders[Math.floor(Math.random() * taskLeaders.length)];
    debugLog(`[DEBUG][selectSchlangengrubeTarget] Ziel gewählt: Spieler ${randomTarget.id} (Zufällig bei Gleichstand)`);
    return {
      id: randomTarget.id,
      reason: "Zufällig bei Gleichstand"
    };
  }

  // Fallback: Ersten gültigen Spieler wählen
  const fallbackTarget = validTargets[0];
  debugLog(`[DEBUG][selectSchlangengrubeTarget] Fallback-Ziel: Spieler ${fallbackTarget.id}`);
  return {
    id: fallbackTarget.id,
    reason: "Fallback-Auswahl"
  };
};

// Hilfsfunktion zur Punkteberechnung
const calculateCurrentPlayerPoints = (snakes, tasks) => {
  let points = 0;

  // Punkte aus Schlangen (vereinfacht)
  snakes.forEach(snake => {
    if (snake && snake.length > 0) {
      points += snake.reduce((sum, card) => sum + (card.points || 0), 0);
    }
  });

  // Punkte aus Aufgaben
  tasks.forEach(task => {
    points += task.points || 0;
  });

  return points;
};

const applyFarbenschutz = (gameState, card) => {
  return {
    ...gameState,
    playerProtected: true,
    toastInfo: { title: "Farbenschutz aktiv!", description: "Du bist vor der nächsten negativen Sonderkarte geschützt." }
  };
};

const applyRegenbogenschlange = (gameState, card, isCurrentPlayerAi = false, currentPlayerId = 0, targetSnakeIndex = 0) => {
  if (isCurrentPlayerAi) {
    // KI-Spieler: Füge zur KI-Schlange hinzu
    const newAiOpponentsData = gameState.aiOpponentsData.map(opp => {
      if (opp.id === currentPlayerId) {
        const newSnakes = [...opp.snakes];
        const snakeIndex = targetSnakeIndex || 0;

        // Stelle sicher, dass die Schlange existiert
        if (!newSnakes[snakeIndex]) {
          newSnakes[snakeIndex] = [];
        }

        newSnakes[snakeIndex] = [...newSnakes[snakeIndex], card];

        return {
          ...opp,
          snakes: newSnakes
        };
      }
      return opp;
    });

    return {
      ...gameState,
      aiOpponentsData: newAiOpponentsData,
      toastInfo: {
        title: `Gegner ${currentPlayerId} spielt Regenbogenschlange!`,
        description: "Ein flexibler Joker für die Schlange."
      }
    };
  } else {
    // Menschlicher Spieler: Originale Logik
    let newPlayerSnakes = gameState.playerSnakes.map(snake => [...snake]);
    const activeSnakeIndex = gameState.activeSnakeIndex === undefined ? 0 : gameState.activeSnakeIndex;
    if (newPlayerSnakes.length === 0) newPlayerSnakes.push([]);
    if(!newPlayerSnakes[activeSnakeIndex]) newPlayerSnakes[activeSnakeIndex] = [];
    newPlayerSnakes[activeSnakeIndex] = [...newPlayerSnakes[activeSnakeIndex], card];
    return {
      ...gameState,
      playerSnakes: newPlayerSnakes,
      toastInfo: { title: "Regenbogenschlange gespielt!", description: "Ein flexibler Joker für deine Schlange." }
    };
  }
};

const applySchlangenblockade = (gameState, card) => {
  let newPlayerSnakes = gameState.playerSnakes.map(snake => [...snake]);
  const activeSnakeIndex = gameState.activeSnakeIndex === undefined ? 0 : gameState.activeSnakeIndex;
  let newPlayerProtected = gameState.playerProtected;
  let newToastInfo;

  if (newPlayerProtected) {
    newPlayerProtected = false;
    newToastInfo = { title: "Farbenschutz verbraucht!", description: "Schlangenblockade wurde abgewehrt." };
  } else {
    if (newPlayerSnakes.length === 0) newPlayerSnakes.push([]);
    if(!newPlayerSnakes[activeSnakeIndex]) newPlayerSnakes[activeSnakeIndex] = [];
    newPlayerSnakes[activeSnakeIndex] = [...newPlayerSnakes[activeSnakeIndex], { ...card, name: 'Blockade', type: 'Blockade', points: 0, color: 'neutral' }];
    newToastInfo = { title: "Schlangenblockade!", description: "Eine Blockade wurde deiner Schlange hinzugefügt." };
  }
  return { ...gameState, playerSnakes: newPlayerSnakes, playerProtected: newPlayerProtected, toastInfo: newToastInfo };
};

const applyVerdoppler = (gameState, isAiPlayer = false, playerId = 0) => {
  const description = isAiPlayer
    ? `Gegner ${playerId} darf diese Runde bis zu 3 Karten spielen (flexibel Farb-/Sonderkarten).`
    : "Du darfst diese Runde bis zu 3 Karten spielen (flexibel Farb-/Sonderkarten).";

  debugLog(`[Apply Verdoppler] Activated for player ${playerId} in turn ${gameState.turnNumber}`);

  return {
    ...gameState,
    toastInfo: { title: "Verdoppler!", description }
  };
};

const handleSchlangenfrassModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.option === 'a') {
    if (payload.selectedCardId && payload.snakeIndex !== undefined) {
      const snakeToRemoveFrom = newGameState.playerSnakes[payload.snakeIndex];
      if (snakeToRemoveFrom) {
        const removedCard = snakeToRemoveFrom.find(c => c.id === payload.selectedCardId);
        newGameState.playerSnakes[payload.snakeIndex] = snakeToRemoveFrom.filter(c => c.id !== payload.selectedCardId);
        if (removedCard) newGameState.discardPile.push(removedCard);
        newGameState.toastInfo = { title: "Schlangenfrass", description: `Karte ${removedCard?.name} aus eigener Schlange entfernt.` };
      }
    }
  } else if (payload.option === 'b') {
     let blockadeRemoved = false;
     newGameState.playerSnakes = newGameState.playerSnakes.map(snake =>
        snake.filter(card => {
            if (card.name === 'Blockade' && !blockadeRemoved) {
                newGameState.discardPile.push(card);
                blockadeRemoved = true;
                return false;
            }
            return true;
        })
     );
     newGameState.toastInfo = blockadeRemoved
        ? { title: "Schlangenfrass", description: "Schlangenblockade entfernt." }
        : { title: "Schlangenfrass", description: "Keine Blockade zum Entfernen gefunden.", variant: "destructive" };
  } else if (payload.option === 'c') {
    let cardsRemovedCount = 0;
    for (let i = 0; i < newGameState.aiOpponentsData.length && cardsRemovedCount < 2; i++) {
      for (let j = 0; j < newGameState.aiOpponentsData[i].snakes.length && cardsRemovedCount < 2; j++) {
        const opponentSnake = newGameState.aiOpponentsData[i].snakes[j];
        if (opponentSnake.length > 0) {
          const removedCard = opponentSnake.pop();
          if (removedCard.name !== 'Blockade') {
            newGameState.discardPile.push(removedCard);
            cardsRemovedCount++;
            newGameState.toastInfo = { title: "Schlangenfrass", description: `${cardsRemovedCount} Karte(n) von Gegnern entfernt.` };
          } else {
            opponentSnake.push(removedCard);
          }
        }
      }
    }
    if (cardsRemovedCount === 0) {
        newGameState.toastInfo = { title: "Schlangenfrass", description: "Keine geeigneten Karten bei Gegnern gefunden.", variant: "destructive" };
    }
  }
  return newGameState;
};

const handleFarbendiebModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.opponentIndex !== undefined && payload.snakeIndex !== undefined && payload.cardIndex !== undefined) {
    const opponent = newGameState.aiOpponentsData[payload.opponentIndex];
    if (opponent && opponent.snakes[payload.snakeIndex]) {
      const stolenCard = opponent.snakes[payload.snakeIndex].splice(payload.cardIndex, 1)[0];
      if (stolenCard) {
        const activeSnakeIdx = newGameState.activeSnakeIndex || 0;
        if (!newGameState.playerSnakes[activeSnakeIdx]) newGameState.playerSnakes[activeSnakeIdx] = [];
        newGameState.playerSnakes[activeSnakeIdx].push(stolenCard);
        newGameState.toastInfo = { title: "Farbendieb!", description: `Karte ${stolenCard.name} von Gegner ${payload.opponentIndex + 1} gestohlen.` };
      }
    }
  }
  return newGameState;
};

const handleSchlangengrubeModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  debugLog('[DEBUG][handleSchlangengrubeModal] payload:', payload);

  // WICHTIG: Prüfe zuerst, ob bereits jemand blockiert ist
  const isAnyPlayerBlocked = newGameState.isBlockedByGrube ||
                           newGameState.aiOpponentsData.some(opp => opp.isBlockedByGrube);

  if (isAnyPlayerBlocked) {
    debugLog(`[DEBUG][handleSchlangengrubeModal] Schlangengrube kann nicht gespielt werden - bereits jemand blockiert`);
    newGameState.toastInfo = {
      title: "Schlangengrube blockiert!",
      description: "Es ist bereits ein Spieler durch eine Schlangengrube blockiert.",
      variant: "destructive"
    };
  } else if (payload.opponentIndex !== undefined) {
    // WICHTIG: Alle anderen Blockierungen aufheben
    newGameState.isBlockedByGrube = false;
    newGameState.aiTurnsWhilePlayerBlocked = 0;

    newGameState.aiOpponentsData = newGameState.aiOpponentsData.map((opp, idx) => ({
      ...opp,
      isBlockedByGrube: idx === payload.opponentIndex
    }));
    newGameState.playerTurnsWhileOpponentBlocked = 0;

    debugLog('[DEBUG][handleSchlangengrubeModal] aiOpponentsData nach Setzen:', newGameState.aiOpponentsData);
    newGameState.toastInfo = { title: "Schlangengrube!", description: `Gegner ${payload.opponentIndex + 1} kann nächste Runde keine Karten anlegen.` };
  }
  return newGameState;
};

const handleFarbenfusionModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.snakeIndex !== undefined && payload.card1Index !== undefined && payload.card2Index !== undefined) {
    const snakeToFuseIn = newGameState.playerSnakes[payload.snakeIndex];
    const card1 = snakeToFuseIn[payload.card1Index];
    const card2 = snakeToFuseIn[payload.card2Index];

    if (card1 && card2 && card1.color === card2.color && Math.abs(payload.card1Index - payload.card2Index) === 1) {
      const fusedCard = {
        id: `fused-${card1.id}-${card2.id}`,
        name: `Fusion: ${card1.name}`,
        type: CARD_TYPES.SPECIAL,
        color: card1.color,
        points: card1.points + card2.points,
        isFusion: true,
        fusedCards: [card1, card2],
        description: `Fusioniert ${card1.name} & ${card2.name}`
      };
      const minIndex = Math.min(payload.card1Index, payload.card2Index);
      snakeToFuseIn.splice(minIndex, 2, fusedCard);
      newGameState.playerSnakes[payload.snakeIndex] = snakeToFuseIn;
      newGameState.toastInfo = { title: "Farbenfusion!", description: `${card1.name} und ${card2.name} wurden fusioniert.` };
    } else {
      newGameState.toastInfo = { title: "Fusion fehlgeschlagen", description: "Karten müssen gleiche Farbe haben und benachbart sein.", variant: "destructive" };
    }
  }
  return newGameState;
};

const handleSchlangenhaeutungModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  if (payload.snakeIndex !== undefined && payload.newOrderArrayOfCards) {
    const snakeToReorder = newGameState.playerSnakes[payload.snakeIndex];
    const prefix = snakeToReorder.slice(0, payload.startIndex);
    const suffix = snakeToReorder.slice(payload.endIndex + 1);

    const moltedSection = payload.newOrderArrayOfCards.map(c => c.id);
    const originalSection = snakeToReorder.slice(payload.startIndex, payload.endIndex + 1).map(c => c.id);

    const originalColors = snakeToReorder.slice(payload.startIndex, payload.endIndex + 1).map(c => c.color).filter(Boolean);
    const newColors = payload.newOrderArrayOfCards.map(c => c.color).filter(Boolean);

    let formedNewGroups = [];

    newGameState.playerSnakes[payload.snakeIndex] = [...prefix, ...payload.newOrderArrayOfCards, ...suffix];

    newGameState.lastMoltingResult = {
        snakeIndex: payload.snakeIndex,
        originalSectionIds: originalSection,
        moltedSectionIds: moltedSection,
        formedNewGroups
    };
    newGameState.toastInfo = { title: "Schlangenhäutung!", description: "Ein Abschnitt deiner Schlange wurde neu geordnet." };
  }
  return newGameState;
};

const handleSchlangenkorbModal = (gameState, payload) => {
  let newGameState = { ...gameState };
  const korbCard = newGameState.discardPile.find(c => c.name === SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK);
  if (korbCard) newGameState.discardPile = newGameState.discardPile.filter(c => c.id !== korbCard.id);

  if (payload.option === 'a') {
    const drawn = newGameState.deck.slice(0, 3);
    newGameState.deck = newGameState.deck.slice(3);
    newGameState.playerHand.push(...drawn);
    newGameState.toastInfo = { title: "Schlangenkorb!", description: "3 Karten gezogen. Spiele bis zu 2 davon." };
    newGameState.schlangenkorbPlayState = { active: true, cardsToPlay: 2, playedCount: 0 };
    return { ...newGameState, cardPlayedFromModal: true };
  } else if (payload.option === 'b') {
    if (payload.handCardIds && payload.snakeCardIds && payload.snakeCardIds.length === payload.handCardIds.length) {
        const snakeToSwapIn = newGameState.playerSnakes[payload.snakeIndex || 0];
        let cardsFromHand = [];
        let cardsFromSnake = [];

        newGameState.playerHand = newGameState.playerHand.filter(hc => {
            if (payload.handCardIds.includes(hc.id)) {
                cardsFromHand.push(hc); return false;
            } return true;
        });

        for (const scId of payload.snakeCardIds) {
            const cardIdx = snakeToSwapIn.findIndex(sc => sc.id === scId);
            if (cardIdx > -1) cardsFromSnake.push(snakeToSwapIn.splice(cardIdx, 1)[0]);
        }

        newGameState.playerHand.push(...cardsFromSnake);
        snakeToSwapIn.push(...cardsFromHand);
        newGameState.playerSnakes[payload.snakeIndex || 0] = snakeToSwapIn;
        newGameState.toastInfo = { title: "Schlangenkorb!", description: `${cardsFromHand.length} Karte(n) getauscht.` };
    }
  } else if (payload.option === 'c') {
    if (payload.target === 'own_blockade') {
        let blockadeRemoved = false;
        newGameState.playerSnakes = newGameState.playerSnakes.map(snake =>
            snake.filter(card => {
                if (card.name === 'Blockade' && !blockadeRemoved) {
                    newGameState.discardPile.push(card);
                    blockadeRemoved = true; return false;
                } return true;
            })
        );
        newGameState.toastInfo = { title: "Schlangenkorb!", description: blockadeRemoved ? "Eigene Blockade entfernt." : "Keine Blockade gefunden."};
    } else if (payload.target === 'opponent_special' && payload.opponentIndex !== undefined && payload.cardId) {
        const oppSnake = newGameState.aiOpponentsData[payload.opponentIndex]?.snakes[payload.snakeIndex || 0];
        if (oppSnake) {
            const cardIdx = oppSnake.findIndex(c => c.id === payload.cardId && c.type === CARD_TYPES.SPECIAL);
            if (cardIdx > -1) {
                const removed = oppSnake.splice(cardIdx, 1)[0];
                newGameState.discardPile.push(removed);
                newGameState.toastInfo = { title: "Schlangenkorb!", description: `Sonderkarte ${removed.name} von Gegner entfernt.`};
            }
        }
    }
  } else if (payload.option === 'd') {
    newGameState.specialCardsPlayedThisTurn = -1;
    newGameState.colorCardsPlayedThisTurn = -1;
    newGameState.toastInfo = { title: "Schlangenkorb!", description: "Du darfst 2 zusätzliche Aktionen ausführen (1 Farbe, 1 Spezial)." };
  }
  return { ...newGameState, cardPlayedFromModal: false };
};

const handleComebackCardModal = (gameState, payload, playCardFn) => {
    let newGameState = { ...gameState };
    newGameState.auxCards.comebackCard.used = true;
    newGameState.comebackActionsRemaining = 2;
    newGameState.comebackActionsRemainingInitial = 2;
    newGameState.comebackActive = true;
    newGameState.toastInfo = { title: "Comeback-Karte aktiviert!", description: "Du hast 2 zusätzliche Aktionen."};

    if (payload.actions && payload.actions.length > 0) {
        for (const action of payload.actions) {
            if (action.type === 'play_color_card' && action.cardId) {
                const card = newGameState.playerHand.find(c => c.id === action.cardId);
                if (card) playCardFn(card, 'Schlangenzone', 'end', true, null, 0);
            } else if (action.type === 'play_special_card' && action.cardId) {
                const card = newGameState.playerHand.find(c => c.id === action.cardId);
                if (card) playCardFn(card, null, null, true, null, 0);
            } else if (action.type === 'discard_draw') {
                if (newGameState.playerHand.length >= 2) {
                    const toDiscard = newGameState.playerHand.slice(0, 2);
                    newGameState.playerHand = newGameState.playerHand.slice(2);
                    newGameState.discardPile.push(...toDiscard);
                    const drawn = newGameState.deck.slice(0, 3);
                    newGameState.deck = newGameState.deck.slice(3);
                    newGameState.playerHand.push(...drawn);
                    newGameState.toastInfo = { title: "Comeback", description: "2 Karten abgeworfen, 3 gezogen."};
                } else {
                    newGameState.toastInfo = { title: "Comeback", description: "Nicht genug Karten zum Abwerfen.", variant: "destructive"};
                }
            }
        }
    }
    return newGameState;
};

const handleRiskRewardCardModal = (gameState, payload, playCardFn) => {
    let newGameState = { ...gameState };
    const riskCardIndex = newGameState.auxCards.riskRewardCards.findIndex(c => !c.used);
    if (riskCardIndex !== -1) {
        newGameState.auxCards.riskRewardCards[riskCardIndex].used = true;
    } else {
        newGameState.toastInfo = { title: "Fehler", description: "Keine Risiko-Belohnungs-Karte verfügbar.", variant: "destructive" };
        return newGameState;
    }

    if (payload.option === 'draw_3') {
        const drawn = newGameState.deck.slice(0, 3);
        newGameState.deck = newGameState.deck.slice(3);
        newGameState.playerHand.push(...drawn);
        newGameState.toastInfo = { title: "Risiko-Belohnung", description: "3 Karten gezogen."};
        if (newGameState.playerHand.length > 5) {
             const toDiscardCount = newGameState.playerHand.length - 5;
             const cardsToDiscard = newGameState.playerHand.slice(0, toDiscardCount);
             newGameState.playerHand = newGameState.playerHand.slice(toDiscardCount);
             newGameState.discardPile = [...newGameState.discardPile, ...cardsToDiscard];
             newGameState.toastInfo.description += ` ${toDiscardCount} überzählige Karte(n) abgelegt.`;
        }
    } else if (payload.option === 'play_extra_color' && payload.cardId) {
        const cardToPlay = newGameState.playerHand.find(c => c.id === payload.cardId && c.type === CARD_TYPES.COLOR);
        if (cardToPlay) {
            playCardFn(cardToPlay, 'Schlangenzone', 'end', true, null, 0);
            newGameState.toastInfo = { title: "Risiko-Belohnung", description: `Zusätzliche Farbkarte ${cardToPlay.name} gespielt.`};
        } else {
            newGameState.toastInfo = { title: "Fehler", description: "Ausgewählte Farbkarte nicht gefunden oder ungültig.", variant: "destructive" };
        }
    } else if (payload.option === 'swap_card' && payload.handCardId && payload.snakeCardId && payload.snakeIndex !== undefined) {
        const handCardIndex = newGameState.playerHand.findIndex(c => c.id === payload.handCardId && c.type === CARD_TYPES.COLOR);
        const snakeCardIndex = newGameState.playerSnakes[payload.snakeIndex].findIndex(c => c.id === payload.snakeCardId && c.type === CARD_TYPES.COLOR);

        if (handCardIndex > -1 && snakeCardIndex > -1) {
            const cardFromHand = newGameState.playerHand[handCardIndex];
            const cardFromSnake = newGameState.playerSnakes[payload.snakeIndex][snakeCardIndex];

            newGameState.playerHand.splice(handCardIndex, 1, cardFromSnake);
            newGameState.playerSnakes[payload.snakeIndex].splice(snakeCardIndex, 1, cardFromHand);
            newGameState.toastInfo = { title: "Risiko-Belohnung", description: `Karten getauscht: ${cardFromHand.name} mit ${cardFromSnake.name}.`};
        } else {
            newGameState.toastInfo = { title: "Fehler", description: "Karten für Tausch nicht gefunden oder ungültig (nur Farbkarten).", variant: "destructive" };
        }
    } else if (payload.option === 'draw_from_discard') {
        if (newGameState.discardPile.length > 0) {
            const drawnCard = newGameState.discardPile.pop();
            newGameState.playerHand.push(drawnCard);
            newGameState.toastInfo = { title: "Risiko-Belohnung", description: `Karte ${drawnCard.name} vom Ablagestapel gezogen.`};
            if (newGameState.playerHand.length > 5) {
                const toDiscardCount = newGameState.playerHand.length - 5;
                const cardsToDiscard = newGameState.playerHand.slice(0, toDiscardCount);
                newGameState.playerHand = newGameState.playerHand.slice(toDiscardCount);
                newGameState.discardPile = [...newGameState.discardPile, ...cardsToDiscard];
                newGameState.toastInfo.description += ` ${toDiscardCount} überzählige Karte(n) abgelegt.`;
            }
        } else {
            newGameState.toastInfo = { title: "Fehler", description: "Ablagestapel ist leer.", variant: "destructive" };
        }
    }
    return newGameState;
};


export const applyCardEffect = (gameState, card, selectedTarget = null, isAuxAction = false) => {
  let tempGameState = { ...gameState };
  let requiresModal = null;

  const isCurrentPlayerAi = gameState.currentPlayerId !== 0;
  const playerState = isCurrentPlayerAi ? gameState.aiOpponentsData.find(opp => opp.id === gameState.currentPlayerId) : gameState;

  // Sicherstellen, dass snakes-Array existiert und mindestens ein leeres Array enthält
  if (isCurrentPlayerAi) {
    if (!playerState.snakes || playerState.snakes.length === 0) playerState.snakes = [[]];
  } else {
    if (!tempGameState.playerSnakes || tempGameState.playerSnakes.length === 0) tempGameState.playerSnakes = [[]];
  }

  const activePlayerSnakeIndex = playerState.activeSnakeIndex === undefined ? 0 : playerState.activeSnakeIndex;
  // Sicherstellen, dass der Index existiert
  if (isCurrentPlayerAi) {
    if (!playerState.snakes[activePlayerSnakeIndex]) playerState.snakes[activePlayerSnakeIndex] = [];
  } else {
    if (!tempGameState.playerSnakes[activePlayerSnakeIndex]) tempGameState.playerSnakes[activePlayerSnakeIndex] = [];
  }
  const activePlayerSnake = isCurrentPlayerAi
    ? (playerState.snakes && playerState.snakes.length > activePlayerSnakeIndex ? playerState.snakes[activePlayerSnakeIndex] || [] : [])
    : (tempGameState.playerSnakes && tempGameState.playerSnakes.length > activePlayerSnakeIndex ? tempGameState.playerSnakes[activePlayerSnakeIndex] || [] : []);

  if(!isAuxAction) {
    tempGameState.playedSpecialCardsHistory = [...(tempGameState.playedSpecialCardsHistory || []), {...card, turnPlayed: gameState.turnNumber, playerId: gameState.currentPlayerId}];
    debugLog(`[Apply Card Effect] Added ${card.name} to history for player ${gameState.currentPlayerId}. New history length: ${tempGameState.playedSpecialCardsHistory.length}`);
  }


  switch (card.name) {
    case SPECIAL_CARDS_NAMES.COLOR_PROTECTION:
      tempGameState = applyFarbenschutz(tempGameState, card);
      break;
    case SPECIAL_CARDS_NAMES.RAINBOW_SNAKE:
      tempGameState = applyRegenbogenschlange(tempGameState, card, isCurrentPlayerAi, gameState.currentPlayerId, targetSnakeIndex);
      break;
    case SPECIAL_CARDS_NAMES.SNAKE_EATER:
      if (!isCurrentPlayerAi) requiresModal = { type: 'SCHLANGENFRASS_OPTIONS', cardName: card.name, cardId: card.id };
      else tempGameState.toastInfo = { title: "KI Aktion", description: "Schlangenfrass von KI (noch nicht implementiert).", duration: 1000}; // Placeholder for AI
      break;
    case SPECIAL_CARDS_NAMES.SNAKE_BLOCKADE:
      tempGameState = applySchlangenblockade(tempGameState, card);
      break;
    case SPECIAL_CARDS_NAMES.COLOR_THIEF:
      if (tempGameState.playerProtected && selectedTarget?.isOpponent) {
         tempGameState.toastInfo = { title: "Farbendieb abgewehrt!", description: `Spieler ${selectedTarget.playerId} ist geschützt.`};
      } else {
        if (!isCurrentPlayerAi) requiresModal = { type: 'FARBENDIEB_CHOOSE_OPPONENT_CARD', cardName: card.name, cardId: card.id, numOpponents: tempGameState.aiOpponentsData.length };
        else tempGameState.toastInfo = { title: "KI Aktion", description: "Farbendieb von KI (noch nicht implementiert).", duration: 1000};
      }
      break;
    case SPECIAL_CARDS_NAMES.SNAKE_PIT:
      if (tempGameState.playerProtected && selectedTarget?.isOpponent) {
        tempGameState.toastInfo = { title: "Schlangengrube abgewehrt!", description: `Spieler ${selectedTarget.playerId} ist geschützt.`};
      } else {
        if (!isCurrentPlayerAi) {
          // WICHTIG: Prüfe zuerst, ob bereits jemand blockiert ist
          const isAnyPlayerBlocked = tempGameState.isBlockedByGrube ||
                                   tempGameState.aiOpponentsData.some(opp => opp.isBlockedByGrube);

          if (isAnyPlayerBlocked) {
            debugLog(`[DEBUG][Spieler-Schlangengrube] Schlangengrube kann nicht gespielt werden - bereits jemand blockiert`);
            tempGameState.toastInfo = {
              title: "Schlangengrube blockiert!",
              description: "Es ist bereits ein Spieler durch eine Schlangengrube blockiert.",
              variant: "destructive"
            };
          } else if (tempGameState.aiOpponentsData.length === 1) {
            // Nur ein Gegner: Flag direkt setzen, kein Modal anzeigen
            const onlyOpponent = tempGameState.aiOpponentsData[0];
            debugLog('[DEBUG] Nur ein Gegner, setze isBlockedByGrube direkt für Gegner', onlyOpponent.id);

            // WICHTIG: Alle anderen Blockierungen aufheben
            tempGameState.isBlockedByGrube = false;
            tempGameState.aiTurnsWhilePlayerBlocked = 0;

            tempGameState.aiOpponentsData = tempGameState.aiOpponentsData.map(opp =>
              opp.id === onlyOpponent.id ? { ...opp, isBlockedByGrube: true } : { ...opp, isBlockedByGrube: false }
            );
            tempGameState.playerTurnsWhileOpponentBlocked = 0;
            debugLog(`[DEBUG] Nach Schlangengrube: isBlockedByGrube=${tempGameState.aiOpponentsData[0].isBlockedByGrube}, playerTurnsWhileOpponentBlocked=${tempGameState.playerTurnsWhileOpponentBlocked}`);
            tempGameState.toastInfo = {
              title: "Schlangengrube!",
              description: `Gegner ${onlyOpponent.id} muss aussetzen. Du darfst einen zusätzlichen Zug ausführen (max. 1 Farbe, 1 Spezial).`
            };
          } else {
            debugLog('[DEBUG] Mehrere Gegner, öffne Gegnerauswahl-Modal für Schlangengrube');
            requiresModal = { type: 'SCHLANGENGRUBE_CHOOSE_OPPONENT', cardName: card.name, cardId: card.id, numOpponents: tempGameState.aiOpponentsData.length };
          }
        } else {
          // WICHTIG: Prüfe zuerst, ob bereits jemand blockiert ist
          const isAnyPlayerBlocked = tempGameState.isBlockedByGrube ||
                                   tempGameState.aiOpponentsData.some(opp => opp.isBlockedByGrube);

          if (isAnyPlayerBlocked) {
            debugLog(`[DEBUG][KI-Schlangengrube] Schlangengrube kann nicht gespielt werden - bereits jemand blockiert`);
            tempGameState.toastInfo = {
              title: "Schlangengrube blockiert!",
              description: "Es ist bereits ein Spieler durch eine Schlangengrube blockiert."
            };
          } else {
            // KI-Logik für Schlangengrube: Intelligente Zielauswahl
            const targetPlayer = selectSchlangengrubeTarget(tempGameState, gameState.currentPlayerId);

            if (targetPlayer.id === 0) {
              // Menschlicher Spieler wurde als Ziel gewählt
              if (!tempGameState.playerProtected) {
                // WICHTIG: Alle anderen Blockierungen aufheben
                tempGameState.aiOpponentsData = tempGameState.aiOpponentsData.map(opp => ({
                  ...opp,
                  isBlockedByGrube: false
                }));
                tempGameState.playerTurnsWhileOpponentBlocked = 0;

                tempGameState.isBlockedByGrube = true;
                tempGameState.aiTurnsWhilePlayerBlocked = 0;
                debugLog(`[DEBUG][KI-Schlangengrube] KI ${gameState.currentPlayerId} blockiert menschlichen Spieler (ID 0) - Grund: ${targetPlayer.reason}`);
                tempGameState.toastInfo = {
                  title: "Schlangengrube!",
                  description: `Du wurdest von Gegner ${gameState.currentPlayerId} blockiert! Du musst eine Runde aussetzen. (${targetPlayer.reason})`
                };
              } else {
                debugLog(`[DEBUG][KI-Schlangengrube] Menschlicher Spieler ist durch Farbenschutz geschützt`);
                tempGameState.toastInfo = {
                  title: "Schlangengrube abgewehrt!",
                  description: "Du bist durch Farbenschutz geschützt."
                };
              }
            } else {
              // KI-Spieler wurde als Ziel gewählt
              const targetAiPlayer = tempGameState.aiOpponentsData.find(opp => opp.id === targetPlayer.id);
              if (targetAiPlayer && !targetAiPlayer.isProtected) {
                // WICHTIG: Alle anderen Blockierungen aufheben
                tempGameState.isBlockedByGrube = false;
                tempGameState.aiTurnsWhilePlayerBlocked = 0;

                tempGameState.aiOpponentsData = tempGameState.aiOpponentsData.map(opp => ({
                  ...opp,
                  isBlockedByGrube: opp.id === targetPlayer.id
                }));
                tempGameState.playerTurnsWhileOpponentBlocked = 0;
                debugLog(`[DEBUG][KI-Schlangengrube] KI ${gameState.currentPlayerId} blockiert KI-Gegner ${targetPlayer.id} - Grund: ${targetPlayer.reason}`);
                tempGameState.toastInfo = {
                  title: "Schlangengrube!",
                  description: `Gegner ${gameState.currentPlayerId} blockiert Gegner ${targetPlayer.id}! (${targetPlayer.reason})`
                };
              } else {
                debugLog(`[DEBUG][KI-Schlangengrube] Ziel-KI ${targetPlayer.id} ist geschützt oder nicht gefunden`);
                tempGameState.toastInfo = {
                  title: "Schlangengrube abgewehrt!",
                  description: `Gegner ${targetPlayer.id} ist geschützt.`
                };
              }
            }
          }
        }
      }
      break;
    case SPECIAL_CARDS_NAMES.COLOR_FUSION:
      if (activePlayerSnake.length < 2) {
        tempGameState.toastInfo = { title: "Zu wenig Karten!", description: "Du brauchst mindestens 2 Karten in der aktiven Schlange für eine Fusion.", variant: "destructive" };
      } else {
        if (!isCurrentPlayerAi) requiresModal = { type: 'FARBENFUSION_CHOOSE_CARDS', cardName: card.name, cardId: card.id, snake: activePlayerSnake, snakeIndex: activePlayerSnakeIndex };
        else tempGameState.toastInfo = { title: "KI Aktion", description: "Farbenfusion von KI (noch nicht implementiert).", duration: 1000};
      }
      break;
    case SPECIAL_CARDS_NAMES.DOUBLER:
      tempGameState = applyVerdoppler(tempGameState, isCurrentPlayerAi, gameState.currentPlayerId);
      debugLog(`[Apply Card Effect] After applyVerdoppler: history length = ${tempGameState.playedSpecialCardsHistory?.length || 0}`);
      break;
    case SPECIAL_CARDS_NAMES.SNAKE_MOLTING:
      if (activePlayerSnake.length < 3) {
        tempGameState.toastInfo = { title: "Zu wenig Karten!", description: "Du brauchst mindestens 3 Karten in der aktiven Schlange für die Häutung.", variant: "destructive" };
      } else {
        if (!isCurrentPlayerAi) requiresModal = { type: 'SCHLANGENHÄUTUNG_CHOOSE_SECTION', cardName: card.name, cardId: card.id, snake: activePlayerSnake, snakeIndex: activePlayerSnakeIndex };
        else tempGameState.toastInfo = { title: "KI Aktion", description: "Schlangenhäutung von KI (noch nicht implementiert).", duration: 1000};
      }
      break;
    case SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK:
      if (!isCurrentPlayerAi) requiresModal = { type: 'SCHLANGENKORB_OPTIONS', cardName: card.name, cardId: card.id };
      else tempGameState.toastInfo = { title: "KI Aktion", description: "Schlangenkorb von KI (noch nicht implementiert).", duration: 1000};
      break;
    default:
      tempGameState.toastInfo = { title: "Unbekannte Sonderkarte", description: "Dieser Effekt ist noch nicht implementiert.", variant: "destructive" };
      break;
  }

  if (!isAuxAction && (card.name !== SPECIAL_CARDS_NAMES.COLOR_PROTECTION || !gameState.isReacting)) {
    if (isCurrentPlayerAi) {
        const aiPlayerIndex = tempGameState.aiOpponentsData.findIndex(opp => opp.id === gameState.currentPlayerId);
        if (aiPlayerIndex !== -1) {
            tempGameState.aiOpponentsData[aiPlayerIndex].hand = tempGameState.aiOpponentsData[aiPlayerIndex].hand.filter(c => c.id !== card.id);
            if (!isAuxAction) tempGameState.aiOpponentsData[aiPlayerIndex].specialCardsPlayedThisTurn = (tempGameState.aiOpponentsData[aiPlayerIndex].specialCardsPlayedThisTurn || 0) + 1;
        }
    } else {
        tempGameState.playerHand = tempGameState.playerHand.filter(c => c.id !== card.id);
        if (!isAuxAction) tempGameState.specialCardsPlayedThisTurn = (tempGameState.specialCardsPlayedThisTurn || 0) + 1;
    }
    tempGameState.discardPile = [...tempGameState.discardPile, card];
  }

  if (tempGameState.schlangenkorbPlayState && tempGameState.schlangenkorbPlayState.active && card.name !== SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK && !isCurrentPlayerAi) {
      tempGameState.schlangenkorbPlayState.playedCount +=1;
      if (tempGameState.schlangenkorbPlayState.playedCount >= tempGameState.schlangenkorbPlayState.cardsToPlay) {
          tempGameState.schlangenkorbPlayState = { active: false, cardsToPlay: 0, playedCount: 0};
      }
  }
  if (tempGameState.comebackActive && !isAuxAction && !isCurrentPlayerAi) {
      tempGameState.comebackActionsRemaining -=1;
      if (tempGameState.comebackActionsRemaining <= 0) {
          tempGameState.comebackActive = false;
          tempGameState.colorCardsPlayedThisTurn = 0;
          tempGameState.specialCardsPlayedThisTurn = 0;
      }
  }

  debugLog(`[Apply Card Effect] RETURN: isCurrentPlayerAi=${isCurrentPlayerAi}, card=${card.name}, history length=${tempGameState.playedSpecialCardsHistory?.length || 0}`);

  return {
    ...tempGameState,
    modalRequired: requiresModal,
    moves: gameState.moves + 1,
    aiOpponentsData: tempGameState.aiOpponentsData.map(opp => ({
      ...opp,
      isBlockedByGrube: opp.isBlockedByGrube
    })),
    playerTurnsWhileOpponentBlocked: tempGameState.playerTurnsWhileOpponentBlocked,
    aiTurnsWhilePlayerBlocked: tempGameState.aiTurnsWhilePlayerBlocked || 0,
    isBlockedByGrube: tempGameState.isBlockedByGrube
  };
};


export const handleModalAction = (gameState, modalType, payload, playCardFn) => {
  let newGameState = { ...gameState, modalRequired: null, toastInfo: null };
  let cardPlayedFromModal = false;

  switch (modalType) {
    case 'SCHLANGENFRASS_OPTIONS':
      newGameState = handleSchlangenfrassModal(newGameState, payload);
      break;
    case 'FARBENDIEB_CHOOSE_OPPONENT_CARD':
      newGameState = handleFarbendiebModal(newGameState, payload);
      break;
    case 'SCHLANGENGRUBE_CHOOSE_OPPONENT':
      newGameState = handleSchlangengrubeModal(newGameState, payload);
      break;
    case 'FARBENFUSION_CHOOSE_CARDS':
      newGameState = handleFarbenfusionModal(newGameState, payload);
      break;
    case 'SCHLANGENHÄUTUNG_CHOOSE_SECTION':
      newGameState = handleSchlangenhaeutungModal(newGameState, payload);
      break;
    case 'SCHLANGENKORB_OPTIONS':
      const korbResult = handleSchlangenkorbModal(newGameState, payload);
      newGameState = korbResult;
      cardPlayedFromModal = korbResult.cardPlayedFromModal;
      break;
    case 'COMEBACK_ACTIONS':
      newGameState = handleComebackCardModal(newGameState, payload, playCardFn);
      break;
    case 'RISK_REWARD_ACTIONS':
      newGameState = handleRiskRewardCardModal(newGameState, payload, playCardFn);
      break;
    default:
      newGameState.toastInfo = { title: "Unbekannte Modal-Aktion", variant: "destructive" };
  }
  return {
    ...newGameState,
    cardPlayedFromModal,
    aiOpponentsData: newGameState.aiOpponentsData.map(opp => ({
      ...opp,
      isBlockedByGrube: opp.isBlockedByGrube
    })),
    playerTurnsWhileOpponentBlocked: newGameState.playerTurnsWhileOpponentBlocked,
    aiTurnsWhilePlayerBlocked: newGameState.aiTurnsWhilePlayerBlocked || 0,
    isBlockedByGrube: newGameState.isBlockedByGrube
  };
};


export const calculateDiversityBonus = (snakes) => {
  let totalBonus = 0;
  snakes.forEach(snake => {
    if (!snake || snake.length === 0) return;
    const colorsInSnake = new Set();
    snake.forEach(card => {
      if (card.type === CARD_TYPES.COLOR) {
        colorsInSnake.add(card.color);
      } else if (card.name === SPECIAL_CARDS_NAMES.RAINBOW_SNAKE) {
        if (colorsInSnake.size < 5) {
            colorsInSnake.add(`Regenbogen-${Math.random()}`);
        }
      }
    });
    const distinctColors = colorsInSnake.size;
    if (distinctColors === 2) totalBonus += 1;
    else if (distinctColors === 3) totalBonus += 2;
    else if (distinctColors === 4) totalBonus += 3;
    else if (distinctColors >= 5) totalBonus += 4;
  });
  return totalBonus;
};

export const checkTaskCompletionLocal = (gameState, taskId, toastCallback) => {
  let newGameState = { ...gameState };

  const taskToCheck = newGameState.openTasks.find(t => t.id === taskId) || (newGameState.secretTask?.id === taskId ? newGameState.secretTask : null);

  if (taskToCheck && !taskToCheck.isCompleted && (!taskToCheck.completedByPlayerId || taskToCheck.completedByPlayerId === newGameState.currentPlayerId)) {
    const contextForCheck = {
        playedSpecialCardsHistory: newGameState.playedSpecialCardsHistory,
        lastMoltingResult: newGameState.lastMoltingResult,
    };
    if (checkTaskCompletion(newGameState.playerSnakes, taskToCheck, contextForCheck)) {

      if (taskToCheck.type === CARD_TYPES.TASK_OPEN) {
        const completedTask = newGameState.openTasks.find(t => t.id === taskId);
        if(completedTask) {
            completedTask.isCompleted = true;
            completedTask.completedByPlayerId = newGameState.currentPlayerId;
            completedTask.completedInLastRound = newGameState.gamePhase === 'lastRound';
            newGameState.playerTasks = [...(newGameState.playerTasks || []), completedTask];
            newGameState.openTasks = newGameState.openTasks.filter(t => t.id !== taskId);

            if (newGameState.availableOpenTaskCards.length > 0) {
                const nextTask = newGameState.availableOpenTaskCards.shift();
                if (nextTask) {
                    newGameState.openTasks.push(nextTask);
                }
            }
        }
      } else if (taskToCheck.type === CARD_TYPES.TASK_SECRET) {
        newGameState.secretTask.isCompleted = true;
      }
      newGameState.toastInfo = { title: "ColorConda!", description: `Aufgabe "${taskToCheck.description.split(':')[0]}" erfüllt!`, className: "bg-primary-gold text-slate-900" };
    } else {
      newGameState.toastInfo = { title: "Bedingung nicht erfüllt", description: "Du erfüllst die Bedingungen dieser Aufgabe noch nicht.", variant: "destructive" };
    }
  } else if (taskToCheck && taskToCheck.isCompleted) {
     newGameState.toastInfo = { title: "Bereits erfüllt", description: "Diese Aufgabe wurde bereits von einem Spieler erfüllt.", variant: "destructive" };
  } else {
    newGameState.toastInfo = { title: "Fehler", description: "Aufgabe konnte nicht geprüft werden.", variant: "destructive"};
  }

  return newGameState;
};

const handlePlayerDrawCardsInternal = (gameState, forPlayerId = 0) => {
  let currentHand, currentDeck;
  let localToastInfo = null;

  if (forPlayerId === 0) { // Human player
    currentHand = [...gameState.playerHand];
    currentDeck = [...gameState.deck];
  } else { // AI player
    const aiPlayer = gameState.aiOpponentsData.find(opp => opp.id === forPlayerId);
    if (!aiPlayer) return gameState; // Should not happen
    currentHand = [...aiPlayer.hand];
    currentDeck = [...gameState.deck]; // AI draws from the same deck
  }

  const cardsToDraw = 5 - currentHand.length;

  if (gameState.gamePhase === 'lastRound') {
      return {...gameState, toastInfo: null};
  }

  if (cardsToDraw > 0 && currentDeck.length > 0) {
    const drawn = currentDeck.slice(0, Math.min(cardsToDraw, currentDeck.length));
    currentHand.push(...drawn);
    currentDeck = currentDeck.slice(drawn.length);
    localToastInfo = { title: `${forPlayerId === 0 ? 'Du hast' : `Gegner ${forPlayerId}`} ${drawn.length} Karte(n) gezogen.`, duration: 2000 };

    if (forPlayerId === 0) {
      return { ...gameState, playerHand: currentHand, deck: currentDeck, toastInfo: localToastInfo, aiOpponentsData: gameState.aiOpponentsData };
    } else {
      const updatedAiOpponents = gameState.aiOpponentsData.map(opp =>
        opp.id === forPlayerId ? { ...opp, hand: currentHand } : opp
      );
      return { ...gameState, aiOpponentsData: updatedAiOpponents, deck: currentDeck, toastInfo: localToastInfo };
    }
  } else if (cardsToDraw > 0 && currentDeck.length === 0) {
    localToastInfo = { title: "Nachziehstapel ist leer!", variant: "destructive", duration: 3000 };
  }
  return {
    ...gameState,
    toastInfo: localToastInfo,
    aiOpponentsData: gameState.aiOpponentsData.map(opp => ({
      ...opp,
      isBlockedByGrube: opp.isBlockedByGrube
    })),
    playerTurnsWhileOpponentBlocked: gameState.playerTurnsWhileOpponentBlocked
  };
};

export const handleEndTurn = (gameState, toastCallback, nextPlayerCallback, endingPlayerId) => {
  debugLog(`[DEBUG][handleEndTurn] START: endingPlayerId=${endingPlayerId}, playerTurnsWhileOpponentBlocked=${gameState.playerTurnsWhileOpponentBlocked}`);
  let newState = {...gameState};

  let currentTurnNumber = newState.turnNumber;
  let nextPlayerId = (endingPlayerId + 1) % newState.numPlayers;

  const isEndingPlayerAi = endingPlayerId !== 0;
  debugLog(`[DEBUG][handleEndTurn] isEndingPlayerAi=${isEndingPlayerAi}`);

  const endingPlayerState = isEndingPlayerAi ? newState.aiOpponentsData.find(opp => opp.id === endingPlayerId) : newState;

  const totalCardsPlayedThisTurn = (endingPlayerState.colorCardsPlayedThisTurn || 0) + (endingPlayerState.specialCardsPlayedThisTurn || 0);

  // Prüfe, ob Spieler mindestens eine Karte spielen muss
  // AUSNAHME: Blockierte Spieler dürfen aussetzen
  const isBlockedAiPlayer = isEndingPlayerAi && endingPlayerState && endingPlayerState.isBlockedByGrube;
  const isBlockedHumanPlayer = !isEndingPlayerAi && newState.isBlockedByGrube;
  debugLog(`[DEBUG][handleEndTurn] totalCardsPlayedThisTurn=${totalCardsPlayedThisTurn}, isBlockedAiPlayer=${isBlockedAiPlayer}, isBlockedHumanPlayer=${isBlockedHumanPlayer}`);

  if (totalCardsPlayedThisTurn === 0 &&
      (isEndingPlayerAi ? endingPlayerState.hand.length > 0 : newState.playerHand.length > 0) &&
      !endingPlayerState.mustPlaySpecialCard &&
      !isBlockedAiPlayer && // <-- Ausnahme für blockierte KI-Spieler
      !isBlockedHumanPlayer && // <-- Ausnahme für blockierte menschliche Spieler
      !(newState.schlangenkorbPlayState && newState.schlangenkorbPlayState.active && newState.schlangenkorbPlayState.playedCount >= newState.schlangenkorbPlayState.cardsToPlay && !isEndingPlayerAi) &&
      !(newState.comebackActive && newState.comebackActionsRemaining === 0 && !isEndingPlayerAi)
     ) {
    newState.toastInfo = { title: "Karte spielen", description: `${isEndingPlayerAi ? `Gegner ${endingPlayerId}` : 'Du'} muss mindestens eine Karte spielen.`, variant: "destructive"};
    debugLog(`[DEBUG][handleEndTurn] EARLY RETURN: Muss Karte spielen`);
    return newState;
  }

  if (!isEndingPlayerAi && newState.playerHand.length > 5 && newState.gamePhase !== 'lastRound') {
      const toDiscardCount = newState.playerHand.length - 5;
      const cardsToDiscard = newState.playerHand.slice(0, toDiscardCount);
      newState.playerHand = newState.playerHand.slice(toDiscardCount);
      newState.discardPile = [...newState.discardPile, ...cardsToDiscard];
      newState.toastInfo = { title: `${toDiscardCount} überzählige Karte(n) abgelegt.` };
  } else if (isEndingPlayerAi && endingPlayerState.hand.length > 5 && newState.gamePhase !== 'lastRound') {
      const toDiscardCount = endingPlayerState.hand.length - 5;
      const cardsToDiscard = endingPlayerState.hand.slice(0, toDiscardCount);
      newState.aiOpponentsData = newState.aiOpponentsData.map(opp =>
        opp.id === endingPlayerId ? {...opp, hand: opp.hand.slice(toDiscardCount)} : opp
      );
      newState.discardPile = [...newState.discardPile, ...cardsToDiscard];
      newState.toastInfo = { title: `Gegner ${endingPlayerId} hat ${toDiscardCount} überzählige Karte(n) abgelegt.` };
  }

  newState = handlePlayerDrawCardsInternal(newState, endingPlayerId);

  if (isEndingPlayerAi) {
    // KI-Spieler beendet seinen Zug
    debugLog(`[DEBUG][handleEndTurn] KI-Spieler ${endingPlayerId} beendet Zug`);
    newState.aiOpponentsData = newState.aiOpponentsData.map(opp =>
        opp.id === endingPlayerId
        ? { ...opp, colorCardsPlayedThisTurn: 0, specialCardsPlayedThisTurn: 0, mustPlaySpecialCard: false }
        : opp
    );
    debugLog('[DEBUG] handleEndTurn: KI-Spieler', endingPlayerId, 'Zug beendet, isBlockedByGrube bleibt erhalten');

    // SCHLANGENGRUBE-LOGIK: Prüfe, ob menschlicher Spieler blockiert ist
    if (newState.isBlockedByGrube) {
      // Erhöhe den Counter für KI-Züge während menschlicher Spieler blockiert ist
      const oldCounter = newState.aiTurnsWhilePlayerBlocked || 0;
      newState.aiTurnsWhilePlayerBlocked = oldCounter + 1;
      debugLog(`[DEBUG] handleEndTurn: KI-Zug beendet, aiTurnsWhilePlayerBlocked von ${oldCounter} erhöht auf: ${newState.aiTurnsWhilePlayerBlocked}`);

      // WICHTIG: Blockierung wird SPÄTER nach der Spieler-Bestimmung aufgehoben
      debugLog(`[DEBUG] handleEndTurn: Menschlicher Spieler bleibt vorerst blockiert (${newState.aiTurnsWhilePlayerBlocked} KI-Züge)`);
    }
  } else {
    // Menschlicher Spieler beendet seinen Zug
    debugLog(`[DEBUG][handleEndTurn] HUMAN PLAYER beendet Zug, playerTurnsWhileOpponentBlocked vorher: ${newState.playerTurnsWhileOpponentBlocked}`);
    debugLog(`[DEBUG][handleEndTurn] HUMAN PLAYER isBlockedByGrube vorher: ${newState.isBlockedByGrube}`);

    newState.colorCardsPlayedThisTurn = 0;
    newState.specialCardsPlayedThisTurn = 0;
    newState.mustPlaySpecialCard = false;
    newState.verdopplerActive = false;
    // WICHTIG: isBlockedByGrube NICHT automatisch zurücksetzen - wird durch KI-Züge gesteuert
    newState.schlangenkorbPlayState = { active: false, cardsToPlay: 0, playedCount: 0 };
    newState.comebackActive = false;
    newState.comebackActionsRemaining = 0;
    newState.comebackActionsRemainingInitial = 0;

    // Erhöhe die Anzahl der tatsächlichen Spielzüge
    newState.playerActualTurns = (newState.playerActualTurns || 0) + 1;

    // Prüfe, ob ein Gegner durch Schlangengrube blockiert ist
    const blockedOpponent = newState.aiOpponentsData.find(opp => opp.isBlockedByGrube);
    debugLog(`[DEBUG][handleEndTurn] Suche blockierte Gegner:`, newState.aiOpponentsData.map(opp => ({id: opp.id, isBlockedByGrube: opp.isBlockedByGrube})));
    debugLog(`[DEBUG][handleEndTurn] blockedOpponent gefunden:`, blockedOpponent ? blockedOpponent.id : 'NONE');

    if (blockedOpponent) {
      // Erhöhe den Counter für Spieler-Züge während Gegner blockiert ist
      const oldCounter = newState.playerTurnsWhileOpponentBlocked || 0;
      newState.playerTurnsWhileOpponentBlocked = oldCounter + 1;
      debugLog(`[DEBUG] handleEndTurn: Spieler-Zug beendet, playerTurnsWhileOpponentBlocked von ${oldCounter} erhöht auf: ${newState.playerTurnsWhileOpponentBlocked}`);

      // Nach 2 Spieler-Zügen wird der Gegner frei
      if (newState.playerTurnsWhileOpponentBlocked >= 2) {
        debugLog('[DEBUG] handleEndTurn: 2 Spieler-Züge erreicht, Gegner wird freigesetzt');
        newState.aiOpponentsData = newState.aiOpponentsData.map(opp =>
          opp.isBlockedByGrube ? { ...opp, isBlockedByGrube: false } : opp
        );
        newState.playerTurnsWhileOpponentBlocked = 0;
        debugLog('[DEBUG] handleEndTurn: Alle blockierten Gegner freigesetzt, Counter zurückgesetzt');
      } else {
        debugLog(`[DEBUG] handleEndTurn: Noch nicht 2 Züge erreicht (${newState.playerTurnsWhileOpponentBlocked}/2), Gegner bleibt blockiert`);
      }
    } else {
      debugLog(`[DEBUG][handleEndTurn] Kein blockierter Gegner gefunden, playerTurnsWhileOpponentBlocked bleibt: ${newState.playerTurnsWhileOpponentBlocked}`);
    }
  }

  newState.selectedCardForPlay = null;
  newState.lastMoltingResult = null;

  // Standard: nächster Spieler
  let finalNextPlayerId = (endingPlayerId + 1) % newState.numPlayers;
  let finalTurnNumber = newState.turnNumber;

  // SCHLANGENGRUBE-LOGIK: Überspringe blockierte Spieler
  if (finalNextPlayerId === 0 && newState.isBlockedByGrube) {
    // Menschlicher Spieler ist blockiert, überspringe ihn
    debugLog('[DEBUG] handleEndTurn: Menschlicher Spieler ist blockiert, überspringe seinen Zug');
    finalNextPlayerId = (finalNextPlayerId + 1) % newState.numPlayers;
  } else if (finalNextPlayerId !== 0) {
    // Prüfe, ob KI-Spieler blockiert ist
    const nextAiPlayer = newState.aiOpponentsData.find(opp => opp.id === finalNextPlayerId);
    if (nextAiPlayer && nextAiPlayer.isBlockedByGrube) {
      debugLog(`[DEBUG] handleEndTurn: KI-Spieler ${finalNextPlayerId} ist blockiert, überspringe seinen Zug`);
      finalNextPlayerId = (finalNextPlayerId + 1) % newState.numPlayers;
    }
  }

  if (finalNextPlayerId === 0) {
    finalTurnNumber += 1;
  }
  newState.turnNumber = finalTurnNumber;
  newState.currentPlayerId = finalNextPlayerId;
  newState.isPlayerTurn = finalNextPlayerId === 0;

  // SCHLANGENGRUBE-LOGIK: Prüfe Freisetzung NACH Spieler-Bestimmung
  // Menschlicher Spieler wird nach einer kompletten Runde freigesetzt (alle anderen Spieler waren dran)
  const totalAiPlayers = newState.numPlayers - 1; // Anzahl KI-Spieler
  if (newState.isBlockedByGrube && newState.aiTurnsWhilePlayerBlocked >= totalAiPlayers) {
    debugLog(`[DEBUG] handleEndTurn: ${totalAiPlayers} KI-Züge erreicht (komplette Runde), menschlicher Spieler wird JETZT freigesetzt`);
    newState.isBlockedByGrube = false;
    newState.aiTurnsWhilePlayerBlocked = 0;
    debugLog('[DEBUG] handleEndTurn: Menschlicher Spieler freigesetzt, Counter zurückgesetzt');
  }

  debugLog(`[DEBUG][handleEndTurn] END: finalNextPlayerId=${finalNextPlayerId}, isBlockedByGrube=${newState.isBlockedByGrube}, playerTurnsWhileOpponentBlocked=${newState.playerTurnsWhileOpponentBlocked}`);

  // Toast-Nachricht anpassen je nach Blockierung
  let toastDescription = `${newState.isPlayerTurn ? 'Du bist' : `Gegner ${newState.currentPlayerId} ist`} an der Reihe.`;
  if (newState.isBlockedByGrube) {
    toastDescription = "Du musst aussetzen (Schlangengrube). Gegner ist am Zug.";
  }
  newState.toastInfo = { title: "Zug beendet", description: toastDescription };

  if (checkGameEndCondition(newState)) {
    if (newState.gamePhase === 'playing') {
        newState.gamePhase = 'lastRound';
        newState.toastInfo = { title: "Letzte Runde!", description: "Der Nachziehstapel ist leer. Dies ist die letzte Runde.", duration: 5000, className: "bg-primary-burgundy text-white" };
    } else if (newState.gamePhase === 'lastRound' && newState.currentPlayerId === 0) {
        newState.gamePhase = 'scoring';
        newState.toastInfo = { title: "Spielende!", description: "Die letzte Runde ist beendet. Punkte werden gezählt.", duration: 5000, className: "bg-primary-gold text-slate-900" };
        newState = calculateFinalScores(newState);
        newState.gamePhase = 'gameOver';
    }
  }

  return {
    ...newState,
    aiOpponentsData: newState.aiOpponentsData.map(opp => ({
      ...opp,
      isBlockedByGrube: opp.isBlockedByGrube
    })),
    playerTurnsWhileOpponentBlocked: newState.playerTurnsWhileOpponentBlocked,
    aiTurnsWhilePlayerBlocked: newState.aiTurnsWhilePlayerBlocked || 0,
    isBlockedByGrube: newState.isBlockedByGrube
  };
};


export const canActivateComebackCard = (gameState) => {
    if (gameState.auxCards.comebackCard.used) return false;

    const playerLongestSnakeLength = Math.max(0, ...gameState.playerSnakes.map(s => s.length));
    let longestTableSnakeLength = playerLongestSnakeLength;

    gameState.aiOpponentsData.forEach(opp => {
        const oppLongestSnake = Math.max(0, ...opp.snakes.map(s => s.length));
        if (oppLongestSnake > longestTableSnakeLength) {
            longestTableSnakeLength = oppLongestSnake;
        }
    });
    return longestTableSnakeLength - playerLongestSnakeLength >= 5;
};

export const checkGameEndCondition = (gameState) => {
    if (gameState.deck.length === 0) return true;
    return false;
};

const calculateColorGroupPoints = (snakes) => {
    let points = 0;
    snakes.forEach(snake => {
        if (!snake || snake.length < 3) return;
        for (let i = 0; i < snake.length; i++) {
            if (snake[i].type === CARD_TYPES.COLOR) {
                const color = snake[i].color;
                let currentLength = 1;
                let groupPoints = snake[i].points;
                for (let j = i + 1; j < snake.length; j++) {
                    if (snake[j].type === CARD_TYPES.COLOR && snake[j].color === color) {
                        currentLength++;
                        groupPoints += snake[j].points;
                    } else {
                        break;
                    }
                }
                if (currentLength === 3) points += groupPoints;
                else if (currentLength === 4) points += groupPoints * 2;
                else if (currentLength >= 5) points += groupPoints * 3;
                i += currentLength -1;
            }
        }
    });
    return points;
};

const findLongestColorChain = (allPlayersSnakes) => {
    let longestChainLength = 0;
    let playersWithLongestChain = [];

    allPlayersSnakes.forEach((player, playerIndex) => {
        player.snakes.forEach(snake => {
            if (!snake || snake.length === 0) return;
            let currentColor = null;
            let currentLength = 0;
            for (const card of snake) {
                if (card.type === CARD_TYPES.COLOR) {
                    if (card.color === currentColor) {
                        currentLength++;
                    } else {
                        currentColor = card.color;
                        currentLength = 1;
                    }
                    if (currentLength > longestChainLength) {
                        longestChainLength = currentLength;
                        playersWithLongestChain = [playerIndex];
                    } else if (currentLength === longestChainLength && !playersWithLongestChain.includes(playerIndex)) {
                        playersWithLongestChain.push(playerIndex);
                    }
                } else {
                    currentColor = null;
                    currentLength = 0;
                }
            }
        });
    });
    return { longestChainLength, playersWithLongestChain };
};


export const calculateFinalScores = (gameState) => {
    let updatedGameState = { ...gameState };
    let playerScores = Array(updatedGameState.numPlayers).fill(0);

    const allPlayersSnakes = [
        { id: 0, snakes: updatedGameState.playerSnakes, tasks: updatedGameState.playerTasks, secretTask: updatedGameState.secretTask },
        ...updatedGameState.aiOpponentsData.map(opp => ({ id: opp.id, snakes: opp.snakes, tasks: opp.tasks || [], secretTask: opp.secretTask }))
    ];

    allPlayersSnakes.forEach((playerData, index) => {
        let score = 0;
        score += calculateColorGroupPoints(playerData.snakes);
        score += calculateDiversityBonus(playerData.snakes);

        playerData.tasks.forEach(task => {
            score += task.completedInLastRound ? task.points * 2 : task.points;
        });
        if (playerData.secretTask && playerData.secretTask.isCompleted) {
            if (checkTaskCompletion(playerData.snakes, playerData.secretTask, updatedGameState)) {
                 score += playerData.secretTask.points;
            }
        }
        playerScores[index] = score;
    });

    const { longestChainLength, playersWithLongestChain } = findLongestColorChain(allPlayersSnakes);
    if (longestChainLength >= 3) {
        playersWithLongestChain.forEach(playerIndex => {
            playerScores[playerIndex] += 5;
        });
    }

    updatedGameState.playerFinalScores = playerScores.map((score, idx) => ({
        playerId: allPlayersSnakes[idx].id,
        name: idx === 0 ? "Du" : `Gegner ${allPlayersSnakes[idx].id}`,
        score
    })).sort((a,b) => b.score - a.score);

    updatedGameState.points = playerScores[0];
    updatedGameState.aiOpponentsData = updatedGameState.aiOpponentsData.map((opp, idx) => ({
        ...opp,
        points: playerScores[idx + 1]
    }));

    return updatedGameState;
};
