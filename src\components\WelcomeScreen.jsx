import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { PlayCircle, ListOrdered, HelpCircle, LogIn, UserPlus } from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext'; 

const WelcomeScreen = () => {
  const { isAuthenticated } = useAuth(); 

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.15,
        duration: 0.5
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 }
    }
  };

  return (
    <motion.div 
      // Hintergrund anpassen, z.B. mit einer der Primärfarben oder einer Textur
      className="min-h-screen flex flex-col items-center justify-center bg-primary-dark-green text-foreground p-4 overflow-hidden"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.header 
        className="text-center mb-8 sm:mb-10"
        variants={itemVariants}
      >
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="w-36 h-36 sm:w-44 sm:h-44 mx-auto mb-5 rounded-full shadow-2xl border-4 border-primary-gold/70 p-1 bg-primary-burgundy/50 flex items-center justify-center">
            {/* Icon-Farbe an das neue Schema anpassen, z.B. Gold */}
            <PlayCircle className="w-20 h-20 sm:w-24 sm:h-24 text-primary-gold" /> 
          </div>
        </motion.div>
        <motion.h1 
          // Überschrift-Farbe, z.B. Gold oder helles Türkis für Kontrast auf Dunkelgrün
          className="text-4xl sm:text-5xl md:text-6xl font-heading font-bold tracking-tight text-primary-gold"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.7 }}
        >
          Card Snake
        </motion.h1>
        <motion.p 
          // Textfarbe anpassen, ggf. helleres Grau oder Off-White
          className="mt-2 text-md sm:text-lg text-slate-200" // Früher foreground/80
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.7 }}
        >
          Das fesselnde Kartenspiel erwacht zum Leben!
        </motion.p>
      </motion.header>

      <motion.main 
        className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5 w-full max-w-xs sm:max-w-xl"
      >
        {!isAuthenticated && (
          <>
            <ButtonMotionWrapper className="sm:col-span-1">
              <Link to="/signin" className="w-full">
                {/* Button-Styling anpassen: Sekundärfarbe (Hellgrau) mit dunklem Text oder Akzentfarbe */}
                <Button variant="default" className="w-full text-md py-2.5 bg-accent text-accent-foreground hover:bg-accent/80 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg focus:ring-4 focus:ring-accent/50">
                  <LogIn className="mr-2 h-5 w-5" />
                  Anmelden
                </Button>
              </Link>
            </ButtonMotionWrapper>
            <ButtonMotionWrapper className="sm:col-span-1">
              <Link to="/signup" className="w-full">
                <Button variant="default" className="w-full text-md py-2.5 bg-accent text-accent-foreground hover:bg-accent/80 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg focus:ring-4 focus:ring-accent/50">
                  <UserPlus className="mr-2 h-5 w-5" />
                  Registrieren
                </Button>
              </Link>
            </ButtonMotionWrapper>
          </>
        )}
        
        <ButtonMotionWrapper className={isAuthenticated ? "sm:col-span-2" : "sm:col-span-2"}>
          <Link to={isAuthenticated ? "/main-menu" : "/config"} className="w-full">
            {/* Haupt-Call-to-Action Button: z.B. Gold oder Burgunderrot */}
            <Button className="w-full text-lg py-3 bg-primary-gold text-gray-900 hover:bg-primary-gold/80 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg focus:ring-4 focus:ring-primary-gold/50">
              <PlayCircle className="mr-2 h-6 w-6" />
              {isAuthenticated ? "Hauptmenü" : "Als Gast spielen"}
            </Button>
          </Link>
        </ButtonMotionWrapper>
        
        <ButtonMotionWrapper className="sm:col-span-1">
           <Link to="/leaderboard" className="w-full">
            {/* Outline-Button mit Akzentfarbe (Türkis) */}
            <Button variant="outline" className="w-full text-md py-2.5 border-accent text-accent hover:bg-accent hover:text-accent-foreground transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg focus:ring-4 focus:ring-accent/50">
              <ListOrdered className="mr-2 h-5 w-5" />
              Bestenliste
            </Button>
          </Link>
        </ButtonMotionWrapper>
        <ButtonMotionWrapper className="sm:col-span-1">
          <Link to="/tutorial" className="w-full">
            <Button variant="outline" className="w-full text-md py-2.5 border-accent text-accent hover:bg-accent hover:text-accent-foreground transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg focus:ring-4 focus:ring-accent/50">
              <HelpCircle className="mr-2 h-5 w-5" />
              Tutorial
            </Button>
          </Link>
        </ButtonMotionWrapper>
      </motion.main>

      <motion.footer 
        // Textfarbe anpassen
        className="absolute bottom-4 sm:bottom-6 text-center text-slate-300 text-xs sm:text-sm" // Früher foreground/70
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8, duration: 0.7 }}
      >
        <p>&copy; {new Date().getFullYear()} Card Snake Masters. Alle Rechte vorbehalten.</p>
        <div className="mt-1">
          {/* Link-Farbe anpassen, z.B. Akzentfarbe */}
          <Link to="/privacy" className="hover:text-accent transition-colors">Datenschutz</Link> | <Link to="/imprint" className="hover:text-accent transition-colors">Impressum</Link>
        </div>
      </motion.footer>
    </motion.div>
  );
};

const ButtonMotionWrapper = ({ children, className = "" }) => {
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 100 }
    }
  };
  return (
    <motion.div
      className={`w-full ${className}`}
      variants={itemVariants} 
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      {children}
    </motion.div>
  );
};

export default WelcomeScreen;