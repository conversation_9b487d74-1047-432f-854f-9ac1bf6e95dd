@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Roboto:wght@400;500;700&family=Open+Sans:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Farben basierend auf "Grafiken und Spieldesign" Dokument */
    --background: 220 10% 10%; /* Dunkelgrau/Blau für hellen Modus Hintergrund */
    --foreground: 0 0% 98%; /* Fast Weiß für Text im hellen Modus */
    
    /* Thematische Primärfarben */
    --primary-dark-green: 120 60% 25%; /* Dunkelgrün */
    --primary-burgundy: 0 50% 30%;    /* Burgunderrot */
    --primary-gold: 40 80% 50%;       /* Gold */

    /* Akzentfarbe */
    --primary: 167 58% 51%; /* Türkis #3AC4AB */
    --primary-foreground: 0 0% 10%; /* Dunkler Text für Kontrast auf Türkis */
    
    --accent: 167 58% 51%; /* Türkis #3AC4AB */
    --accent-foreground: 0 0% 10%;

    /* Kartenhintergrund */
    --card: 120 60% 25%; 
    --card-foreground: 0 0% 100%; /* Weißer Text auf Karten */

    --popover: 120 60% 20%; 
    --popover-foreground: 0 0% 100%;

    /* Sekundärfarben */
    --secondary: 220 10% 25%; 
    --secondary-foreground: 0 0% 90%;
    
    --muted: 220 10% 30%; 
    --muted-foreground: 0 0% 60%; 

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 10% 35%; 
    --input: 220 10% 20%; 
    --ring: 167 58% 51%; 

    --radius: 0.5rem;
  }

  .dark {
    --background: 120 60% 15%; 
    --foreground: 0 0% 95%;   
    
    --primary-dark-green: 120 60% 25%;
    --primary-burgundy: 0 50% 30%;   
    --primary-gold: 40 80% 50%;      

    --primary: 167 58% 61%; 
    --primary-foreground: 0 0% 10%; 
    
    --accent: 167 58% 61%; 
    --accent-foreground: 0 0% 10%;

    --card: 120 50% 20%; 
    --card-foreground: 0 0% 95%;

    --popover: 120 50% 18%;
    --popover-foreground: 0 0% 95%;

    --secondary: 120 30% 30%; 
    --secondary-foreground: 0 0% 95%;
    
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 65%;

    --destructive: 0 70% 50%; 
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 25%; 
    --input: 0 0% 22%;  
    --ring: 167 58% 71%; 
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans; 
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading; 
  }
}

@layer components {
  .texture-paper {
    background-image: url('/assets/textures/paper.png'); 
    background-blend-mode: multiply;
    opacity: 0.8;
  }
  .texture-fabric {
    background-image: url('/assets/textures/fabric.png'); 
    background-blend-mode: overlay;
    opacity: 0.5;
  }
}
