import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-primary-dark-green to-slate-800 text-white p-4">
          <div className="max-w-2xl mx-auto text-center">
            <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-6 backdrop-blur-md">
              <h1 className="text-3xl font-bold text-red-400 mb-4">🐍 Oops! Etwas ist schiefgelaufen</h1>
              <p className="text-lg text-gray-300 mb-6">
                Das Spiel ist auf einen unerwarteten Fehler gestoßen. Keine Sorge, das passiert manchmal!
              </p>
              
              <div className="space-y-4">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-burgundy hover:bg-primary-burgundy/80 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  🔄 Spiel neu laden
                </button>
                
                <button
                  onClick={() => {
                    localStorage.clear();
                    window.location.reload();
                  }}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors ml-4"
                >
                  🗑️ Daten löschen & neu starten
                </button>
              </div>

              {process.env.NODE_ENV === 'development' && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-yellow-400 font-semibold">
                    🔧 Entwickler-Details (nur im Debug-Modus)
                  </summary>
                  <div className="mt-4 p-4 bg-black/30 rounded border border-gray-600 text-sm">
                    <h3 className="text-red-400 font-bold mb-2">Fehler:</h3>
                    <pre className="text-red-300 whitespace-pre-wrap mb-4">
                      {this.state.error && this.state.error.toString()}
                    </pre>
                    
                    <h3 className="text-yellow-400 font-bold mb-2">Stack Trace:</h3>
                    <pre className="text-gray-300 whitespace-pre-wrap text-xs">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </div>
                </details>
              )}
              
              <div className="mt-6 text-sm text-gray-400">
                <p>Wenn das Problem weiterhin besteht, versuche:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Browser-Cache leeren</li>
                  <li>Browser neu starten</li>
                  <li>Andere Browser verwenden</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
