import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';
import { Mail } from 'lucide-react';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [step, setStep] = useState(1); // 1 for email, 2 for new password (after clicking email link)
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { forgotPassword, resetPassword: authResetPassword } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    const hash = window.location.hash;
    if (hash.includes('type=recovery') || hash.includes('access_token=')) { // Supabase adds type=recovery or access_token
      setStep(2); 
    }
  }, []);


  const handleRequestReset = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    const { error: requestError } = await forgotPassword(email);
    setLoading(false);
    if (requestError) {
      setError(requestError.message || "Fehler beim Anfordern des Passwort-Resets.");
      toast({ title: "Fehler", description: requestError.message || "Konnte Reset-Anfrage nicht senden.", variant: "destructive" });
    } else {
      toast({ title: "E-Mail gesendet", description: "Wenn ein Konto mit dieser E-Mail existiert, wurde ein Link zum Zurücksetzen gesendet." });
      // User will click link in email, which will bring them to this page again, useEffect will detect token and set step = 2
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    const { error: resetError } = await authResetPassword(newPassword);
    setLoading(false);
    if (resetError) {
      setError(resetError.message || "Fehler beim Zurücksetzen des Passworts.");
      toast({ title: "Fehler", description: resetError.message || "Passwort konnte nicht zurückgesetzt werden.", variant: "destructive" });
    } else {
      toast({ title: "Passwort zurückgesetzt", description: "Dein Passwort wurde erfolgreich geändert. Du kannst dich jetzt anmelden." });
      navigate('/signin');
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-900 via-primary-dark-green to-slate-800 p-4">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="w-full max-w-md bg-slate-800 text-slate-100 border-primary-gold shadow-xl">
          <CardHeader className="text-center">
            <img-replace src="/logo.png" alt="ColorConda Logo" className="w-20 h-20 mx-auto mb-4 rounded-full shadow-md border-2 border-primary-gold"/>
            <CardTitle className="text-3xl font-heading text-primary-gold">Passwort vergessen?</CardTitle>
            {step === 1 && <CardDescription className="text-slate-400">Kein Problem! Gib deine E-Mail-Adresse ein, um einen Link zum Zurücksetzen zu erhalten.</CardDescription>}
            {step === 2 && <CardDescription className="text-slate-400">Gib dein neues Passwort ein.</CardDescription>}
          </CardHeader>
          <CardContent>
            {error && <p className="text-red-500 text-sm bg-red-900/30 p-3 rounded-md mb-4 text-center">{error}</p>}
            
            {step === 1 && (
              <form onSubmit={handleRequestReset} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-slate-300">E-Mail</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                    <Input 
                      id="email" 
                      type="email" 
                      value={email} 
                      onChange={(e) => setEmail(e.target.value)} 
                      required 
                      className="pl-10 bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100 placeholder:text-slate-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <Button type="submit" className="w-full bg-primary-gold hover:bg-primary-gold/80 text-slate-900 font-semibold py-3" disabled={loading}>
                  {loading ? 'Sende...' : 'Link zum Zurücksetzen senden'}
                </Button>
              </form>
            )}

            {step === 2 && (
              <form onSubmit={handleResetPassword} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="newPassword" className="text-slate-300">Neues Passwort</Label>
                  <Input 
                    id="newPassword" 
                    type="password" 
                    value={newPassword} 
                    onChange={(e) => setNewPassword(e.target.value)} 
                    required 
                    minLength="6"
                    className="bg-slate-700 border-slate-600 focus:border-primary-gold text-slate-100"
                    placeholder="••••••••"
                  />
                </div>
                <Button type="submit" className="w-full bg-primary-gold hover:bg-primary-gold/80 text-slate-900 font-semibold py-3" disabled={loading}>
                  {loading ? 'Setze zurück...' : 'Passwort zurücksetzen'}
                </Button>
              </form>
            )}
          </CardContent>
          <CardFooter className="flex flex-col items-center space-y-3 pt-6">
            <p className="text-sm text-slate-400">
              Zurück zum <Link to="/signin" className="text-primary-gold hover:underline">Login</Link>
            </p>
            <Link to="/" className="text-xs text-slate-500 hover:text-primary-gold transition-colors">
              Zurück zur Startseite
            </Link>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
};

export default ForgotPasswordPage;