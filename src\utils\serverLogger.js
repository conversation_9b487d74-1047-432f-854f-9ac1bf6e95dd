// Server-basiertes Logging-System für persistente Logs
// Sendet Logs an einen lokalen Endpunkt, der sie in Dateien schreibt

const LOG_ENDPOINT = '/api/logs';
const isServer = typeof window === 'undefined';

// Formatiere Log-Eintrag
const formatLogEntry = (level, message, ...args) => {
  const timestamp = new Date().toISOString();
  const argsStr = args.length > 0 ? ' ' + args.map(arg => 
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
  ).join(' ') : '';
  return `[${timestamp}] [${level}] ${message}${argsStr}`;
};

// Sende Log an Server
const sendLogToServer = async (logEntry) => {
  if (isServer) return; // Nur im Browser ausführen
  
  try {
    await fetch(LOG_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        logEntry,
        timestamp: new Date().toISOString()
      })
    });
  } catch (error) {
    // Fehler beim Senden ignorieren, um Endlosschleifen zu vermeiden
    console.warn('Fehler beim Senden des Logs an Server:', error.message);
  }
};

// Exportierte Log-Funktionen
export const serverLog = (message, ...args) => {
  const logEntry = formatLogEntry('INFO', message, ...args);
  console.log(message, ...args); // Immer in Konsole
  sendLogToServer(logEntry); // Zusätzlich an Server
};

export const serverWarn = (message, ...args) => {
  const logEntry = formatLogEntry('WARN', message, ...args);
  console.warn(message, ...args);
  sendLogToServer(logEntry);
};

export const serverError = (message, ...args) => {
  const logEntry = formatLogEntry('ERROR', message, ...args);
  console.error(message, ...args);
  sendLogToServer(logEntry);
};

// Hole Logs vom Server
export const getServerLogs = async () => {
  if (isServer) return '';
  
  try {
    const response = await fetch(LOG_ENDPOINT);
    if (response.ok) {
      return await response.text();
    }
    return '';
  } catch (error) {
    console.warn('Fehler beim Abrufen der Server-Logs:', error);
    return '';
  }
};

// Lösche Server-Logs
export const clearServerLogs = async () => {
  if (isServer) return;
  
  try {
    await fetch(LOG_ENDPOINT, { method: 'DELETE' });
    console.log('Server-Logs gelöscht');
  } catch (error) {
    console.warn('Fehler beim Löschen der Server-Logs:', error);
  }
};
