import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/AuthContext';

import WelcomeScreen from '@/components/WelcomeScreen';
import Tutorial from '@/components/Tutorial';
import GameConfig from '@/components/GameConfig';
import GameBoard from '@/components/GameBoard';
import Leaderboard from '@/components/Leaderboard';
import PrivacyPage from '@/pages/PrivacyPage';
import ImprintPage from '@/pages/ImprintPage';
import SignInPage from '@/pages/SignInPage';
import SignUpPage from '@/pages/SignUpPage';
import ForgotPasswordPage from '@/pages/ForgotPasswordPage';
import ProfilePage from '@/pages/ProfilePage';
import MainMenu from '@/components/MainMenu'; 
import SettingsPage from '@/pages/SettingsPage'; // Import der neuen Seite
import ProtectedRoute from '@/components/shared/ProtectedRoute';


function App() {
  return (
    <AuthProvider>
      <Routes>
        {/* Öffentliche Routen */}
        <Route path="/" element={<WelcomeScreen />} />
        <Route path="/signin" element={<SignInPage />} />
        <Route path="/signup" element={<SignUpPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/privacy" element={<PrivacyPage />} />
        <Route path="/imprint" element={<ImprintPage />} />
        
        <Route path="/tutorial" element={<Tutorial />} />
        <Route path="/leaderboard" element={<Leaderboard />} />

        {/* Geschützte Routen (erfordern Anmeldung) */}
        <Route path="/main-menu" element={<ProtectedRoute><MainMenu /></ProtectedRoute>} />
        <Route path="/config" element={<ProtectedRoute><GameConfig /></ProtectedRoute>} />
        <Route path="/game" element={<ProtectedRoute><GameBoard /></ProtectedRoute>} />
        <Route path="/profile" element={<ProtectedRoute><ProfilePage /></ProtectedRoute>} />
        <Route path="/settings" element={<ProtectedRoute><SettingsPage /></ProtectedRoute>} />


        {/* Fallback-Route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
      <Toaster />
    </AuthProvider>
  );
}

export default App;