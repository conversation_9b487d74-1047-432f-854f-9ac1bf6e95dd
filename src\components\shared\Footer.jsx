import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

const Footer = ({ className }) => {
  const currentYear = new Date().getFullYear();

  return (
    <motion.footer 
      className={`w-full py-6 text-center ${className || 'text-slate-400 bg-slate-800/50'}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.5 }}
    >
      <div className="container mx-auto px-4">
        <p className="text-sm">
          &copy; {currentYear} Card Snake Masters. Alle Rechte vorbehalten.
        </p>
        <div className="mt-2">
          <Link to="/privacy" className="text-xs hover:text-primary-gold transition-colors duration-300 mx-2">
            Datenschutz
          </Link>
          <span className="text-xs">|</span>
          <Link to="/imprint" className="text-xs hover:text-primary-gold transition-colors duration-300 mx-2">
            Impressum
          </Link>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;
