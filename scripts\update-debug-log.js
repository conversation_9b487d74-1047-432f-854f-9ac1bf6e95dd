#!/usr/bin/env node

/**
 * Script zum Aktualisieren der debug.log Datei mit Browser-Logs
 * 
 * Verwendung:
 * 1. Spiele das Spiel und sammle Logs
 * 2. <PERSON><PERSON><PERSON> Browser-Konsole und führe aus: showLogsForCopy()
 * 3. <PERSON>pie<PERSON> den Inhalt
 * 4. <PERSON>ühre dieses Script aus: node scripts/update-debug-log.js
 * 5. Füge den kopierten Inhalt ein wenn gefragt
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function updateDebugLog() {
  console.log('🔧 Debug-Log Updater für Schlangentanz');
  console.log('=====================================');
  console.log('');
  console.log('1. <PERSON><PERSON><PERSON> das Spiel im Browser');
  console.log('2. <PERSON><PERSON><PERSON> die Browser-Konsole (F12)');
  console.log('3. Führe aus: showLogsForCopy()');
  console.log('4. Kopiere den angezeigten Log-Inhalt');
  console.log('5. Füge ihn hier ein (beende mit einer leeren Zeile):');
  console.log('');

  const logLines = [];
  
  return new Promise((resolve) => {
    rl.on('line', (line) => {
      if (line.trim() === '') {
        // Leere Zeile = Ende der Eingabe
        if (logLines.length > 0) {
          const logContent = logLines.join('\n');
          const logPath = path.join(__dirname, '..', 'logs', 'debug.log');
          
          try {
            fs.writeFileSync(logPath, logContent, 'utf8');
            console.log('');
            console.log(`✅ Debug-Log erfolgreich aktualisiert: ${logPath}`);
            console.log(`📊 Anzahl Zeilen: ${logLines.length}`);
          } catch (error) {
            console.error('❌ Fehler beim Schreiben der Log-Datei:', error);
          }
          
          rl.close();
          resolve();
        } else {
          console.log('❌ Keine Log-Daten eingegeben. Abbruch.');
          rl.close();
          resolve();
        }
      } else {
        logLines.push(line);
      }
    });
  });
}

updateDebugLog().then(() => {
  console.log('🏁 Script beendet.');
  process.exit(0);
});
