import React from 'react';
import { motion } from 'framer-motion';
import SnakeDisplay from '@/components/gameboard/SnakeDisplay';
import { UserCircle } from 'lucide-react';

const OpponentDisplay = ({ opponentData, isActivePlayer }) => {
  if (!opponentData) {
    return null;
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  return (
    <motion.div 
      variants={itemVariants}
      className={`p-2 sm:p-3 rounded-lg border transition-all duration-300 ease-in-out
                  ${isActivePlayer ? 'bg-purple-800/40 border-purple-500 shadow-lg' : 'bg-slate-700/40 border-slate-600'}`}
    >
      <div className="flex items-center mb-1 sm:mb-2">
        <UserCircle className={`h-5 w-5 sm:h-6 sm:w-6 mr-2 ${isActivePlayer ? 'text-purple-300' : 'text-slate-400'}`} />
        <h3 className={`text-md sm:text-lg font-semibold ${isActivePlayer ? 'text-purple-200' : 'text-slate-300'}`}>
          {opponentData.name}
          {opponentData.isBlockedByGrube && <span className="text-xs text-red-400 ml-2">(Blockiert)</span>}
        </h3>
      </div>
      
      {opponentData.snakes && opponentData.snakes.map((snake, index) => (
        <div key={`opponent-${opponentData.id}-snake-${index}`} className="mt-1">
          <h4 className="text-xs text-slate-400 mb-0.5">Schlange {index + 1} ({snake.length} Karten)</h4>
          <SnakeDisplay
            snake={snake}
            snakeId={`opponent-${opponentData.id}-snake-${index}`}
            isPlayerTurn={false} 
            selectedCardId={null}
            isActive={false} 
            isOpponentSnake={true}
          />
        </div>
      ))}
      {(!opponentData.snakes || opponentData.snakes.length === 0) && (
        <p className="text-xs text-slate-500 italic">Noch keine Schlangen</p>
      )}
    </motion.div>
  );
};

export default OpponentDisplay;