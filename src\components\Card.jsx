import React, { useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { gsap } from 'gsap';
import { motion } from 'framer-motion';
import { Gem, Shield, Zap, Star, HelpCircle } from 'lucide-react';

const ItemTypes = {
  CARD: 'card',
};

const Card = ({ id, text, type, points, description, onPlay, onDragStart }) => {
  const cardRef = React.useRef(null);

  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemTypes.CARD,
    item: () => {
      if (onDragStart) {
        onDragStart({ id, text, type, points, description });
      }
      return { id, text, type, points, description };
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (item && dropResult && dropResult.zoneName) {
      }
    },
  }));

  useEffect(() => {
    if (cardRef.current) {
      if (isDragging) {
        gsap.to(cardRef.current, { scale: 1.1, boxShadow: "0px 10px 20px rgba(0,0,0,0.3)", y: -10, duration: 0.2 });
      } else {
        gsap.to(cardRef.current, { scale: 1, boxShadow: "0px 4px 8px rgba(0,0,0,0.2)", y: 0, duration: 0.2 });
      }
    }
  }, [isDragging]);
  
  let bgColor = 'bg-card'; 
  let textColor = 'text-card-foreground'; 
  let icon = <HelpCircle className="w-4/5 h-4/5 opacity-20" />;

  if (type) {
    switch (type.toLowerCase()) {
      case 'blau': bgColor = 'bg-blue-600'; icon = <Gem className="w-full h-full opacity-70" />; break;
      case 'rot': bgColor = 'bg-red-600'; icon = <Zap className="w-full h-full opacity-70" />; break;
      case 'gelb': bgColor = 'bg-yellow-500'; textColor = 'text-slate-900'; icon = <Star className="w-full h-full opacity-70" />; break;
      case 'violett': bgColor = 'bg-purple-600'; icon = <Gem className="w-full h-full opacity-70" />; break;
      case 'braun': bgColor = 'bg-orange-700'; icon = <Shield className="w-full h-full opacity-70" />; break;
      case 'grün': bgColor = 'bg-green-600'; icon = <Gem className="w-full h-full opacity-70" />; break;
      default: 
        if(type.toLowerCase().includes("schutz") || type.toLowerCase().includes("blockade")) {
            bgColor = 'bg-slate-500'; icon = <Shield className="w-full h-full opacity-70" />;
        } else if (type.toLowerCase().includes("regenbogen") || type.toLowerCase().includes("fusion")) {
            bgColor = 'bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-500'; icon = <Star className="w-full h-full opacity-70" />;
        } else {
            bgColor = 'bg-slate-700'; 
            icon = <Zap className="w-full h-full opacity-70" />;
        }
    }
  }


  return (
    <motion.div
      ref={(node) => {
        drag(node); 
        cardRef.current = node; 
      }}
      className={`p-2 m-0.5 w-20 h-28 sm:p-2 sm:m-1 sm:w-24 sm:h-36 ${bgColor} ${textColor} rounded-lg shadow-lg cursor-grab flex flex-col justify-between items-center select-none relative overflow-hidden border-2 border-black/30 ${isDragging ? 'opacity-60 ring-4 ring-primary-gold transform rotate-3' : 'opacity-100'}`}
      whileHover={{ y: -3, scale: 1.03, boxShadow: "0px 6px 12px rgba(0,0,0,0.3)" }}
      transition={{ type: "spring", stiffness: 400, damping: 15 }}
      layoutId={`card-${id}`} 
    >
      <div className="absolute inset-0 flex items-center justify-center z-0 opacity-30">
        {icon}
      </div>
      <div className="relative z-10 w-full flex justify-between items-start">
        <span className="text-xs sm:text-sm font-bold font-heading">{text}</span>
        {points > 0 && (
          <span className="text-xs sm:text-sm font-bold bg-primary-gold text-slate-900 px-1.5 py-0.5 rounded-full shadow-sm">
            {points}P
          </span>
        )}
      </div>
      
      <div className="relative z-10 text-center">
        <span className="text-[0.6rem] sm:text-xs opacity-90 leading-tight block px-0.5">{type}</span>
      </div>
      
      <div className="relative z-10 w-full text-right">
        <span className="text-[0.55rem] sm:text-[0.65rem] opacity-70 font-mono block truncate px-0.5" title={description}>
          {id.substring(0,10)}
        </span>
      </div>
    </motion.div>
  );
};

export default Card;
