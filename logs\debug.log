Schlangentanz Debug Log
======================
Erstellt: 2024-12-19T10:00:00.000Z
Zweck: Debug-Ausgaben für Schlangengrube-Analyse und KI-History-Duplikate

HINWEIS: Diese Datei wird manuell von der Anwendung beschrieben.
Logs werden hier gesammelt, damit die KI darauf zugreifen kann.

AKTUELLE PROBLEME:
- KI-History zeigt Duplikate (4x Regenbogenschlange statt 1x)
- Automatisches Spielende beim Fensterwechsel

KORREKTUREN IMPLEMENTIERT:
- Duplikat-Schutz in aiHistoryUtils.js
- Page Visibility Hook für Fensterwechsel-Schutz
- Verbesserte Debug-Logs

=== LOG EINTRÄGE ===

