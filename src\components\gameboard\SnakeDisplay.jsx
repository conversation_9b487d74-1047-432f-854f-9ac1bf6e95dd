import React from 'react';
import { motion } from 'framer-motion';
import Card from '@/components/Card';
import DropZone from '@/components/gameboard/DropZone';

const SnakeDisplay = ({ snake, onDrop, onClickOnPosition, isPlayerTurn, selectedCardId, snakeId, isActive }) => {
  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1, transition: { type: "spring", stiffness: 300, damping: 20 }}
  };
  
  const placeholderText = isActive ? "Karten hier ablegen..." : "Nicht aktiv";

  const handleZoneClick = (position) => {
    if (onClickOnPosition && isPlayerTurn && isActive && selectedCardId) {
      onClickOnPosition(position);
    }
  };

  return (
    <div className="flex items-center justify-center my-1 p-1 rounded-lg bg-slate-900/30 min-h-[120px] sm:min-h-[160px]">
      <DropZone 
        onDrop={(item) => onDrop(item, 'start')} 
        onClick={() => handleZoneClick('start')}
        zoneName={`snake-${snakeId}-start`}
        className={`mr-1 sm:mr-2 w-16 h-24 sm:w-20 sm:h-32 flex-shrink-0 ${!isPlayerTurn || !isActive ? 'border-slate-600' : 'border-primary-gold/70'} ${isPlayerTurn && isActive && selectedCardId ? 'cursor-pointer hover:bg-primary-gold/10' : ''}`}
        isActive={isPlayerTurn && isActive && !!selectedCardId}
      >
        <span className="text-xs text-slate-400">{snake && snake.length > 0 ? "Anfang" : placeholderText}</span>
      </DropZone>
      
      <div className="flex-grow flex flex-nowrap overflow-x-auto items-center justify-start h-full scrollbar-thin scrollbar-thumb-slate-700 scrollbar-track-slate-800 px-1">
        {(!snake || snake.length === 0) && isActive && (
          <span className="text-slate-500 italic text-center w-full">Leere Schlange</span>
        )}
        {snake && snake.map((card, index) => (
          <motion.div
            key={card.id || `snake-${snakeId}-card-${index}`}
            variants={itemVariants}
            initial="hidden"
            animate="visible"
            className="flex-shrink-0 mx-0.5 transform scale-75 sm:scale-90"
          >
            <Card 
              id={card.id} 
              text={card.name} 
              type={card.color || card.type} 
              points={card.points} 
              description={card.description} 
            />
          </motion.div>
        ))}
      </div>

      <DropZone 
        onDrop={(item) => onDrop(item, 'end')} 
        onClick={() => handleZoneClick('end')}
        zoneName={`snake-${snakeId}-end`}
        className={`ml-1 sm:ml-2 w-16 h-24 sm:w-20 sm:h-32 flex-shrink-0 ${!isPlayerTurn || !isActive ? 'border-slate-600' : 'border-primary-gold/70'} ${isPlayerTurn && isActive && selectedCardId ? 'cursor-pointer hover:bg-primary-gold/10' : ''}`}
        isActive={isPlayerTurn && isActive && !!selectedCardId}
      >
        <span className="text-xs text-slate-400">Ende</span>
      </DropZone>
    </div>
  );
};

export default SnakeDisplay;
