import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, CheckCircle, Zap, Shuffle, Target as TargetIcon, Info } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';

const tutorialStepsData = [
  {
    title: "Willkommen bei Card Snake!",
    content: "Lerne die Grundlagen von Card Snake. Klicke 'Weiter', um zu beginnen.",
    imagePlaceholder: "Einladendes Bild zum Spielstart",
    icon: Info,
    bgColor: "from-indigo-500 to-purple-600",
  },
  {
    title: "Das Spielbrett verstehen",
    content: "Das Spielbrett hat mehrere Bereiche: De<PERSON> (unten), die zentrale 'Schlange' (Mitte) und den Ablagestapel.",
    imagePlaceholder: "Übersicht des Spielbretts mit Zonen",
    icon: Zap,
    bgColor: "from-sky-500 to-cyan-600",
  },
  {
    title: "<PERSON><PERSON> ziehen & spielen",
    content: "Zu Beginn deines Zuges ziehst du Karten. Spiele Karten passend an die Schlange an, um sie zu verlängern.",
    imagePlaceholder: "Animation einer Karte, die gezogen und angelegt wird",
    icon: Shuffle,
    bgColor: "from-emerald-500 to-green-600",
  },
  {
    title: "Punkte sammeln",
    content: "Bestimmte Kartenkombinationen oder das Erreichen von Längenzielen der Schlange geben Punkte.",
    imagePlaceholder: "Visualisierung von Punkten, die einem Spieler gutgeschrieben werden",
    icon: TargetIcon,
    bgColor: "from-amber-500 to-yellow-600",
  },
  {
    title: "Ziel des Spiels",
    content: "Erreiche eine bestimmte Punktzahl oder erfülle andere Siegbedingungen, um das Spiel zu gewinnen.",
    imagePlaceholder: "Siegesbildschirm oder Trophäe",
    icon: CheckCircle,
    bgColor: "from-rose-500 to-pink-600",
  },
  {
    title: "Tutorial abgeschlossen!",
    content: "Du bist jetzt bereit, Card Snake zu meistern! Viel Spaß und strategisches Geschick!",
    imagePlaceholder: "Feierliches Bild zum Abschluss",
    icon: CheckCircle,
    bgColor: "from-purple-600 to-indigo-700",
  }
];

const Tutorial = () => {
  const [currentStep, setCurrentStep] = React.useState(0);

  const nextStep = () => {
    setCurrentStep((prev) => Math.min(prev + 1, tutorialStepsData.length - 1));
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const step = tutorialStepsData[currentStep];
  const IconComponent = step.icon;

  return (
    <div className={`min-h-screen flex flex-col items-center justify-center bg-gradient-to-br ${step.bgColor} text-white p-4 transition-colors duration-500 ease-in-out`}>
      <motion.div 
        key={currentStep} // Wichtig für AnimatePresence bei Komponentenwechsel
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.5, type: "spring", stiffness:120 }}
        className="bg-slate-800/70 backdrop-blur-md p-6 sm:p-8 rounded-xl shadow-2xl w-full max-w-xl text-center"
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={step.title} // Wechselt, wenn sich der Titel ändert, löst Animation aus
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ duration: 0.4 }}
            className="flex flex-col items-center"
          >
            <IconComponent className="w-16 h-16 text-yellow-400 mb-4" />
            <h2 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 text-slate-100">{step.title}</h2>
            <div className="w-full h-48 sm:h-64 bg-slate-700 rounded-lg my-4 sm:my-6 flex items-center justify-center text-slate-400 italic">
              <img
                alt={step.title || "Tutorial Schritt"}
                className="max-w-full max-h-full object-contain rounded-lg"
                src={`https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=60&h=300&description=${encodeURIComponent(step.imagePlaceholder)}`}
              />
            </div>
            <p className="text-base sm:text-lg text-slate-300 mb-6 sm:mb-8 min-h-[50px] sm:min-h-[60px]">{step.content}</p>
          </motion.div>
        </AnimatePresence>

        <div className="flex justify-between items-center mt-6 sm:mt-8">
          <Button onClick={prevStep} disabled={currentStep === 0} variant="outline" className="text-slate-300 border-slate-500 hover:bg-slate-700 hover:text-white transition-colors">
            <ChevronLeft className="mr-1 sm:mr-2 h-5 w-5" />
            Zurück
          </Button>
          <div className="flex space-x-1.5 sm:space-x-2">
            {tutorialStepsData.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full transition-all duration-300 ${currentStep === index ? 'bg-yellow-400 scale-125' : 'bg-slate-600 hover:bg-slate-500'}`}
                aria-label={`Gehe zu Schritt ${index + 1}`}
              />
            ))}
          </div>
          {currentStep === tutorialStepsData.length - 1 ? (
            <Button asChild className="bg-green-500 hover:bg-green-600 transition-colors">
              <Link to="/">
                <CheckCircle className="mr-1 sm:mr-2 h-5 w-5" />
                Abschließen
              </Link>
            </Button>
          ) : (
            <Button onClick={nextStep} className="bg-yellow-500 hover:bg-yellow-600 transition-colors">
              Weiter
              <ChevronRight className="ml-1 sm:ml-2 h-5 w-5" />
            </Button>
          )}
        </div>
      </motion.div>
       <Button asChild variant="link" className="mt-6 text-sm text-slate-300 hover:text-yellow-300 transition-colors">
         <Link to="/">Tutorial überspringen</Link>
       </Button>
    </div>
  );
};

export default Tutorial;