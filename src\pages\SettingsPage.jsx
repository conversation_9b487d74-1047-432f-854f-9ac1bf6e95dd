import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings, ArrowLeft, Volume2, Palette, Accessibility, ShieldQuestion } from 'lucide-react';
import { motion } from 'framer-motion';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input'; // Für Checkbox/Toggle

const SettingsPage = () => {
  // Placeholder state for settings
  const [soundEnabled, setSoundEnabled] = React.useState(true);
  const [reducedMotion, setReducedMotion] = React.useState(false);
  const [theme, setTheme] = React.useState('dark'); // 'dark', 'light', 'system'

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } }
  };

  const itemVariants = {
    hidden: { opacity:0, x: -20 },
    visible: (custom) => ({ opacity:1, x: 0, transition: { delay: custom * 0.1 }})
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-800 p-4">
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        className="w-full max-w-lg"
      >
        <Card className="bg-slate-800/80 backdrop-blur-md border-slate-700 text-white shadow-2xl">
          <CardHeader className="text-center">
            <Settings className="mx-auto h-12 w-12 text-indigo-400 mb-4" />
            <CardTitle className="text-3xl font-bold text-indigo-300">Einstellungen</CardTitle>
            <CardDescription className="text-slate-400">Passe dein Spielerlebnis an.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <motion.div custom={0} variants={itemVariants} initial="hidden" animate="visible" className="p-4 bg-slate-700/50 rounded-lg">
              <Label htmlFor="sound-toggle" className="text-lg flex items-center text-slate-200 mb-2">
                <Volume2 className="mr-3 h-6 w-6 text-indigo-400" />
                Soundeffekte
              </Label>
              <div className="flex items-center justify-between">
                <span className="text-slate-300">{soundEnabled ? "Aktiviert" : "Deaktiviert"}</span>
                <Input 
                  type="checkbox" 
                  id="sound-toggle" 
                  checked={soundEnabled} 
                  onChange={() => setSoundEnabled(!soundEnabled)}
                  className="form-checkbox h-6 w-6 text-indigo-500 bg-slate-600 border-slate-500 rounded focus:ring-indigo-400 cursor-pointer"
                />
              </div>
            </motion.div>

            <motion.div custom={1} variants={itemVariants} initial="hidden" animate="visible" className="p-4 bg-slate-700/50 rounded-lg">
              <Label htmlFor="motion-toggle" className="text-lg flex items-center text-slate-200 mb-2">
                <Accessibility className="mr-3 h-6 w-6 text-indigo-400" />
                Reduzierte Bewegung
              </Label>
              <div className="flex items-center justify-between">
                <span className="text-slate-300">{reducedMotion ? "Aktiviert" : "Deaktiviert"}</span>
                 <Input 
                  type="checkbox" 
                  id="motion-toggle" 
                  checked={reducedMotion} 
                  onChange={() => setReducedMotion(!reducedMotion)}
                  className="form-checkbox h-6 w-6 text-indigo-500 bg-slate-600 border-slate-500 rounded focus:ring-indigo-400 cursor-pointer"
                />
              </div>
              <p className="text-xs text-slate-400 mt-1">Deaktiviert aufwendige Animationen für bessere Leistung.</p>
            </motion.div>
            
            <motion.div custom={2} variants={itemVariants} initial="hidden" animate="visible" className="p-4 bg-slate-700/50 rounded-lg">
              <Label htmlFor="theme-select" className="text-lg flex items-center text-slate-200 mb-2">
                <Palette className="mr-3 h-6 w-6 text-indigo-400" />
                Farbschema
              </Label>
              <select 
                id="theme-select" 
                value={theme} 
                onChange={(e) => setTheme(e.target.value)}
                className="w-full p-2.5 rounded-md bg-slate-700 border border-slate-600 text-white focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
              >
                <option value="dark">Dunkel</option>
                <option value="light" disabled>Hell (Demnächst)</option>
                <option value="system" disabled>System (Demnächst)</option>
              </select>
            </motion.div>

            <motion.div custom={3} variants={itemVariants} initial="hidden" animate="visible">
              <Button className="w-full text-md py-3 bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600">
                Einstellungen speichern
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
      <motion.div initial={{ opacity:0 }} animate={{ opacity:1 }} transition={{ delay: 0.5 }}>
        <Button asChild variant="link" className="mt-8 text-sm text-slate-400 hover:text-indigo-300 transition-colors">
          <Link to="/main-menu">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Zurück zum Hauptmenü
          </Link>
        </Button>
      </motion.div>
    </div>
  );
};

export default SettingsPage;