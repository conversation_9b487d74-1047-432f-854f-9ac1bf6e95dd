import React, { useState } from 'react';
import { SPECIAL_CARDS_NAMES } from '@/utils/gameRules';

// Debug-Utilities für Entwicklungsmodus
export const isDebugMode = () => {
  return import.meta.env.VITE_DEBUG_MODE === 'true' || import.meta.env.DEV;
};

// Log-Datei Management
let logEntries = [];
const MAX_LOG_ENTRIES = 1000; // Begrenze die Anzahl der Log-Einträge

const formatLogEntry = (timestamp, message, ...args) => {
  const timeStr = timestamp.toISOString().split('T')[1].split('.')[0]; // HH:MM:SS
  const fullMessage = [message, ...args.map(arg =>
    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
  )].join(' ');
  return `[${timeStr}] ${fullMessage}`;
};

// Throttling für häufige Nachrichten
const messageThrottle = new Map();
const THROTTLE_TIME = 2000; // 2 Sekunden

const shouldLogMessage = (message) => {
  const now = Date.now();
  const lastLogged = messageThrottle.get(message);

  if (!lastLogged || now - lastLogged > THROTTLE_TIME) {
    messageThrottle.set(message, now);
    return true;
  }
  return false;
};

export const debugLog = (message, ...args) => {
  if (isDebugMode()) {
    const timestamp = new Date();

    // Prüfe ob diese Nachricht geloggt werden soll (Throttling)
    const isImportant = message.includes('Schlangengrube') || message.includes('blockiert') ||
        message.includes('KARTE GESPIELT') || message.includes('LIMIT ERREICHT') ||
        message.includes('Regenbogenschlange') || message.includes('KI-History') ||
        message.includes('[ERROR]') || message.includes('[WARN]');

    const shouldLog = isImportant || shouldLogMessage(message);

    if (shouldLog) {
      console.log(message, ...args);

      // Füge zur Log-Datei hinzu
      const logEntry = formatLogEntry(timestamp, message, ...args);
      logEntries.push(logEntry);

      // Begrenze die Anzahl der Einträge
      if (logEntries.length > MAX_LOG_ENTRIES) {
        logEntries = logEntries.slice(-MAX_LOG_ENTRIES);
      }

      // Speichere nur bei wichtigen Events
      if (isImportant) {
        saveLogsToStorage();

        console.group('🚨 WICHTIGES DEBUG-EVENT');
        console.log(`Event: ${message}`);
        console.log(`Zeit: ${timestamp.toISOString()}`);
        console.groupEnd();
      }
    }
  }
};

export const debugWarn = (message, ...args) => {
  if (isDebugMode()) {
    const timestamp = new Date();
    console.warn(message, ...args);

    // Füge zur Log-Datei hinzu
    const logEntry = formatLogEntry(timestamp, `[WARN] ${message}`, ...args);
    logEntries.push(logEntry);

    if (logEntries.length > MAX_LOG_ENTRIES) {
      logEntries = logEntries.slice(-MAX_LOG_ENTRIES);
    }

    // AUTOMATISCH SPEICHERN: Bei jedem Warn-Eintrag
    saveLogsToStorage();
  }
};

export const debugError = (message, ...args) => {
  if (isDebugMode()) {
    const timestamp = new Date();
    console.error(message, ...args);

    // Füge zur Log-Datei hinzu
    const logEntry = formatLogEntry(timestamp, `[ERROR] ${message}`, ...args);
    logEntries.push(logEntry);

    if (logEntries.length > MAX_LOG_ENTRIES) {
      logEntries = logEntries.slice(-MAX_LOG_ENTRIES);
    }

    // AUTOMATISCH SPEICHERN: Bei jedem Error-Eintrag
    saveLogsToStorage();
  }
};

// Exportiere Logs als Datei
export const downloadLogFile = () => {
  if (logEntries.length === 0) {
    console.warn('Keine Log-Einträge zum Download verfügbar');
    return;
  }

  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  const logContent = [
    `Schlangentanz Debug Log - ${timestamp}`,
    `Generiert: ${new Date().toISOString()}`,
    `Anzahl Einträge: ${logEntries.length}`,
    '='.repeat(50),
    '',
    ...logEntries
  ].join('\n');

  const blob = new Blob([logContent], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `schlangentanz-debug-${timestamp}.log`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  console.log(`Debug-Log heruntergeladen: ${logEntries.length} Einträge`);
};

// Zeige aktuelle Logs zum manuellen Kopieren
export const showLogsForCopy = () => {
  const logContent = localStorage.getItem('schlangentanz_current_log');
  if (!logContent) {
    console.warn('❌ Keine Logs in localStorage gefunden');
    return;
  }

  console.group('📋 AKTUELLE LOGS ZUM KOPIEREN IN DEBUG.LOG:');
  console.log('='.repeat(60));
  console.log('KOPIERE DEN FOLGENDEN INHALT IN logs/debug.log:');
  console.log('='.repeat(60));
  console.log(logContent);
  console.log('='.repeat(60));
  console.groupEnd();

  return logContent;
};

// Automatische Log-Anzeige alle 10 Sekunden (nur im Debug-Modus)
let autoLogInterval = null;

export const startAutoLogDisplay = () => {
  if (!isDebugMode() || autoLogInterval) return;

  autoLogInterval = setInterval(() => {
    if (logEntries.length > 0) {
      console.group('🔄 AUTO-LOG-UPDATE (alle 30s)');
      console.log(`Aktuelle Logs: ${logEntries.length} Einträge`);
      console.log('Letzte wichtige Einträge:');

      // Zeige nur wichtige Einträge der letzten 30 Sekunden
      const recentImportant = logEntries.filter(entry =>
        entry.includes('Schlangengrube') || entry.includes('blockiert') ||
        entry.includes('KARTE GESPIELT') || entry.includes('KI-History') ||
        entry.includes('[ERROR]') || entry.includes('[WARN]')
      ).slice(-5);

      if (recentImportant.length > 0) {
        recentImportant.forEach((entry, index) => {
          console.log(`${index + 1}: ${entry}`);
        });
      } else {
        console.log('Keine wichtigen Events in letzter Zeit');
      }

      console.log('Verwende showLogsForCopy() für vollständige Logs');
      console.groupEnd();
    }
  }, 30000); // Alle 30 Sekunden statt 10

  console.log('[AutoLog] Automatische Log-Anzeige gestartet (alle 30s)');
};

export const stopAutoLogDisplay = () => {
  if (autoLogInterval) {
    clearInterval(autoLogInterval);
    autoLogInterval = null;
    debugLog('[AutoLog] Automatische Log-Anzeige gestoppt');
  }
};

// Speichere Logs auch in localStorage für persistente Analyse
export const saveLogsToStorage = () => {
  if (logEntries.length === 0) return;

  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    entries: logEntries,
    stats: getLogStats()
  };

  try {
    localStorage.setItem('schlangentanz_debug_logs', JSON.stringify(logData));
    console.log(`Logs in localStorage gespeichert: ${logEntries.length} Einträge`);

    // Zusätzlich: Schreibe in Datei-System (falls möglich)
    writeLogsToFile();
  } catch (error) {
    console.warn('Fehler beim Speichern der Logs:', error);
  }
};

// Schreibe Logs direkt in Datei (vereinfacht für automatisches Logging)
const writeLogsToFile = async () => {
  try {
    const timestamp = new Date().toISOString().split('T')[0];
    const logContent = [
      `Schlangentanz Debug Log - ${timestamp}`,
      `Generiert: ${new Date().toISOString()}`,
      `Anzahl Einträge: ${logEntries.length}`,
      '='.repeat(50),
      '',
      ...logEntries,
      '',
      '=== ENDE LOG ===',
      ''
    ].join('\n');

    // Speichere in localStorage für persistente Analyse
    try {
      localStorage.setItem('schlangentanz_current_log', logContent);
      console.log(`✅ Logs in localStorage gespeichert: ${logEntries.length} Einträge`);
    } catch (error) {
      console.warn('❌ Fehler beim Speichern in localStorage:', error);
    }

    // Zeige Logs direkt in Konsole für manuelles Kopieren (kein HTTP-Request)
    console.group('📋 LOGS FÜR MANUELLE DATEI-ERSTELLUNG (logs/debug.log)');
    console.log('='.repeat(60));
    console.log('KOPIERE DEN FOLGENDEN INHALT IN logs/debug.log:');
    console.log('='.repeat(60));
    console.log(logContent);
    console.log('='.repeat(60));
    console.log('✅ Logs bereit zum Kopieren - Fügen Sie sie in logs/debug.log ein');
    console.groupEnd();

  } catch (error) {
    console.log('ℹ️ Log-Schreibung fehlgeschlagen:', error);
  }
};

// Lade Logs aus localStorage
export const loadLogsFromStorage = () => {
  try {
    const stored = localStorage.getItem('schlangentanz_debug_logs');
    if (stored) {
      const logData = JSON.parse(stored);
      return logData;
    }
  } catch (error) {
    console.warn('Fehler beim Laden der Logs:', error);
  }
  return null;
};

// Lösche alle Log-Einträge
export const clearLogs = () => {
  logEntries = [];
  console.log('Debug-Logs gelöscht');
};

// Hole aktuelle Log-Einträge (für Anzeige)
export const getCurrentLogs = () => {
  return [...logEntries];
};

// Hole Log-Statistiken
export const getLogStats = () => {
  return {
    totalEntries: logEntries.length,
    maxEntries: MAX_LOG_ENTRIES,
    oldestEntry: logEntries.length > 0 ? logEntries[0] : null,
    newestEntry: logEntries.length > 0 ? logEntries[logEntries.length - 1] : null
  };
};

// Analysiere Logs auf Schlangengrube-Probleme
export const analyzeSchlangengrubeLogs = (logs = null) => {
  const logsToAnalyze = logs || logEntries;
  const analysis = {
    schlangengrubeEvents: [],
    blockingEvents: [],
    multipleBlockingIssues: [],
    summary: {}
  };

  logsToAnalyze.forEach((logEntry, index) => {
    const entry = logEntry.toLowerCase();

    // Schlangengrube-Events finden
    if (entry.includes('schlangengrube')) {
      analysis.schlangengrubeEvents.push({
        index,
        entry: logEntry,
        type: entry.includes('ki-schlangengrube') ? 'AI' :
              entry.includes('spieler-schlangengrube') ? 'PLAYER' : 'UNKNOWN'
      });
    }

    // Blockierungs-Events finden
    if (entry.includes('blockiert') || entry.includes('blocked')) {
      analysis.blockingEvents.push({
        index,
        entry: logEntry,
        isBlocking: entry.includes('blockiert') && !entry.includes('nicht'),
        target: entry.includes('menschlichen spieler') ? 'HUMAN' :
                entry.includes('ki-gegner') ? 'AI' : 'UNKNOWN'
      });
    }

    // Mehrfach-Blockierung-Probleme
    if (entry.includes('bereits jemand blockiert') ||
        entry.includes('kann nicht gespielt werden')) {
      analysis.multipleBlockingIssues.push({
        index,
        entry: logEntry
      });
    }
  });

  // Zusammenfassung erstellen
  analysis.summary = {
    totalSchlangengrubeEvents: analysis.schlangengrubeEvents.length,
    totalBlockingEvents: analysis.blockingEvents.length,
    multipleBlockingIssues: analysis.multipleBlockingIssues.length,
    aiSchlangengrubeEvents: analysis.schlangengrubeEvents.filter(e => e.type === 'AI').length,
    playerSchlangengrubeEvents: analysis.schlangengrubeEvents.filter(e => e.type === 'PLAYER').length,
    humanBlockedEvents: analysis.blockingEvents.filter(e => e.target === 'HUMAN' && e.isBlocking).length,
    aiBlockedEvents: analysis.blockingEvents.filter(e => e.target === 'AI' && e.isBlocking).length
  };

  return analysis;
};

// Zeige Schlangengrube-Analyse in der Konsole
export const showSchlangengrubeAnalysis = () => {
  const stored = loadLogsFromStorage();
  const logs = stored ? stored.entries : logEntries;

  if (logs.length === 0) {
    console.log('🔍 Keine Logs für Analyse verfügbar');
    return;
  }

  const analysis = analyzeSchlangengrubeLogs(logs);

  console.group('🕳️ SCHLANGENGRUBE-ANALYSE');
  console.log('📊 Zusammenfassung:', analysis.summary);

  if (analysis.multipleBlockingIssues.length > 0) {
    console.group('🚨 MEHRFACH-BLOCKIERUNG-PROBLEME');
    analysis.multipleBlockingIssues.forEach(issue => {
      console.log(`[${issue.index}] ${issue.entry}`);
    });
    console.groupEnd();
  }

  if (analysis.schlangengrubeEvents.length > 0) {
    console.group('🎯 SCHLANGENGRUBE-EVENTS');
    analysis.schlangengrubeEvents.forEach(event => {
      console.log(`[${event.index}] ${event.type}: ${event.entry}`);
    });
    console.groupEnd();
  }

  if (analysis.blockingEvents.length > 0) {
    console.group('🚫 BLOCKIERUNGS-EVENTS');
    analysis.blockingEvents.forEach(event => {
      console.log(`[${event.index}] ${event.target} ${event.isBlocking ? 'BLOCKED' : 'UNBLOCKED'}: ${event.entry}`);
    });
    console.groupEnd();
  }

  console.groupEnd();

  return analysis;
};

// Debug-Funktionen für Spielzustand
export const dumpGameState = (gameState, label = 'GameState') => {
  if (isDebugMode()) {
    console.group(`🐍 [DEBUG] ${label}`);
    console.log('Current Player:', gameState.currentPlayerId);
    console.log('Turn Number:', gameState.turnNumber);
    console.log('Player Hand:', gameState.playerHand?.length || 0, 'cards');
    console.log('AI Opponents:', gameState.aiOpponentsData?.map(ai => ({
      id: ai.id,
      handSize: ai.hand?.length || 0,
      isBlocked: ai.isBlockedByGrube,
      colorCardsPlayed: ai.colorCardsPlayedThisTurn || 0,
      specialCardsPlayed: ai.specialCardsPlayedThisTurn || 0
    })));
    console.log('Deck size:', gameState.deck?.length || 0);
    console.log('Discard pile:', gameState.discardPile?.length || 0);
    console.groupEnd();
  }
};

// Special Card Testing Configuration
export const SPECIAL_CARD_TEST_CONFIG = {
  [SPECIAL_CARDS_NAMES.DOUBLER]: {
    name: 'Verdoppler',
    emoji: '⚡',
    description: 'Erlaubt bis zu 3 Karten flexibel (Farbe/Spezial)',
    priority: 'HIGH',
    testNotes: 'Sollte zuerst gespielt werden, dann 2 weitere Karten'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_PIT]: {
    name: 'Schlangengrube',
    emoji: '🕳️',
    description: 'Blockiert Gegner für eine Runde',
    priority: 'HIGH',
    testNotes: 'Sollte Gegner auswählen und blockieren'
  },
  [SPECIAL_CARDS_NAMES.COLOR_THIEF]: {
    name: 'Farbendieb',
    emoji: '🎭',
    description: 'Stiehlt Karte von Gegnerschlange',
    priority: 'MEDIUM',
    testNotes: 'Sollte beste Karte von Gegner stehlen'
  },
  [SPECIAL_CARDS_NAMES.COLOR_FUSION]: {
    name: 'Farbenfusion',
    emoji: '🔗',
    description: 'Fusioniert zwei benachbarte Karten gleicher Farbe',
    priority: 'MEDIUM',
    testNotes: 'Benötigt 2+ Karten gleicher Farbe in Schlange'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_MOLTING]: {
    name: 'Schlangenhäutung',
    emoji: '🐍',
    description: 'Ordnet Schlangenabschnitt neu',
    priority: 'MEDIUM',
    testNotes: 'Benötigt 3+ Karten in Schlange'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_EATER]: {
    name: 'Schlangenfrass',
    emoji: '🍽️',
    description: 'Entfernt Karten von eigener/gegnerischer Schlange',
    priority: 'MEDIUM',
    testNotes: 'Sollte strategisch Karten entfernen'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_BLOCKADE]: {
    name: 'Schlangenblockade',
    emoji: '🚧',
    description: 'Fügt Blockade zur Schlange hinzu',
    priority: 'LOW',
    testNotes: 'Negative Karte, sollte vermieden werden'
  },
  [SPECIAL_CARDS_NAMES.COLOR_PROTECTION]: {
    name: 'Farbenschutz',
    emoji: '🛡️',
    description: 'Schutz vor nächster negativer Sonderkarte',
    priority: 'MEDIUM',
    testNotes: 'Defensiv, sollte bei Bedrohung gespielt werden'
  },
  [SPECIAL_CARDS_NAMES.RAINBOW_SNAKE]: {
    name: 'Regenbogenschlange',
    emoji: '🌈',
    description: 'Flexibler Joker für Schlange',
    priority: 'HIGH',
    testNotes: 'Sollte strategisch platziert werden'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK]: {
    name: 'Schlangenkorb',
    emoji: '🧺',
    description: 'Verschiedene Glücksoptionen',
    priority: 'MEDIUM',
    testNotes: 'Sollte beste Option wählen'
  }
};

// Debug-Panel Komponente für erweiterte Informationen
export const DebugPanel = ({ gameState, onAction }) => {
  const [selectedSpecialCard, setSelectedSpecialCard] = useState(SPECIAL_CARDS_NAMES.SNAKE_PIT);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isDebugMode()) return null;

  const handleQuickAction = (action, params = {}) => {
    debugLog(`[QUICK-ACTION] ${action}`, params);
    if (onAction) onAction(action, params);
  };

  const aiOpponent = gameState.aiOpponentsData?.[0] || {};
  const hasVerdoppler = aiOpponent.hand?.some(c => c.name === 'Verdoppler') || false;
  const verdopplerCount = aiOpponent.hand?.filter(c => c.name === 'Verdoppler').length || 0;

  // Detaillierte Kartenauflistung
  const aiHandCards = aiOpponent.hand || [];
  const colorCards = aiHandCards.filter(c => c.type === 'color' || c.type === 'COLOR');
  const specialCards = aiHandCards.filter(c => c.type === 'special' || c.type === 'SPECIAL');

  // Special Card Testing
  const selectedCardConfig = SPECIAL_CARD_TEST_CONFIG[selectedSpecialCard];
  const hasSelectedCard = aiOpponent.hand?.some(c => c.name === selectedSpecialCard) || false;
  const selectedCardCount = aiOpponent.hand?.filter(c => c.name === selectedSpecialCard).length || 0;

  return (
    <div className="fixed top-32 right-4 bg-red-900/90 backdrop-blur-md text-white p-3 rounded-lg border border-red-600 z-50 min-w-[320px] max-w-[420px]">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-bold text-red-200">🔧 DEBUG PANEL</h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-xs bg-red-700 hover:bg-red-600 px-2 py-1 rounded transition-colors"
        >
          {isExpanded ? '▼' : '▶'} {isExpanded ? 'Weniger' : 'Mehr'}
        </button>
      </div>

      <div className="space-y-2 text-xs">
        {/* Basic Game State */}
        <div className="bg-black/30 p-2 rounded">
          <div><strong>Deck:</strong> {gameState.deck?.length || 0} Karten</div>
          <div><strong>Abwurf:</strong> {gameState.discardPile?.length || 0} Karten</div>
          <div><strong>Phase:</strong> {gameState.gamePhase}</div>
          <div><strong>KI Verdoppler:</strong> {verdopplerCount} in Hand ({hasVerdoppler ? '✅' : '❌'})</div>
        </div>

        {/* Special Card Testing Section */}
        <div className="bg-blue-900/30 p-2 rounded border border-blue-600/50">
          <div className="font-bold text-blue-200 mb-2">🎴 SONDERKARTEN-TEST</div>

          {/* Special Card Selector */}
          <div className="mb-2">
            <label className="block text-xs text-blue-300 mb-1">Karte auswählen:</label>
            <select
              value={selectedSpecialCard}
              onChange={(e) => setSelectedSpecialCard(e.target.value)}
              className="w-full bg-black/50 border border-blue-500/50 rounded px-2 py-1 text-xs text-white"
            >
              {Object.entries(SPECIAL_CARD_TEST_CONFIG).map(([cardName, config]) => (
                <option key={cardName} value={cardName}>
                  {config.emoji} {config.name} ({config.priority})
                </option>
              ))}
            </select>
          </div>

          {/* Selected Card Info */}
          {selectedCardConfig && (
            <div className="bg-black/30 p-2 rounded mb-2 border border-gray-600/50">
              <div className="font-semibold text-yellow-300">
                {selectedCardConfig.emoji} {selectedCardConfig.name}
              </div>
              <div className="text-gray-300 text-xs mt-1">{selectedCardConfig.description}</div>
              <div className="text-blue-300 text-xs mt-1 italic">{selectedCardConfig.testNotes}</div>
              <div className="mt-1">
                <span className={`text-xs px-1 py-0.5 rounded ${
                  selectedCardConfig.priority === 'HIGH' ? 'bg-red-700 text-red-200' :
                  selectedCardConfig.priority === 'MEDIUM' ? 'bg-yellow-700 text-yellow-200' :
                  'bg-gray-700 text-gray-200'
                }`}>
                  {selectedCardConfig.priority} PRIORITY
                </span>
              </div>
              <div className="mt-1 text-xs">
                <strong>KI hat:</strong> {selectedCardCount} x {selectedCardConfig.name} ({hasSelectedCard ? '✅' : '❌'})
              </div>
            </div>
          )}

          {/* Test Actions */}
          <div className="flex flex-col gap-1">
            <button
              onClick={() => handleQuickAction('give_ai_special_card', { cardName: selectedSpecialCard })}
              className="bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
            >
              {selectedCardConfig?.emoji} KI {selectedCardConfig?.name} geben
            </button>
            <button
              onClick={() => showLogsForCopy()}
              className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
            >
              📋 Logs in Konsole anzeigen
            </button>
          </div>
        </div>

        {/* Expanded AI Hand Details */}
        {isExpanded && (
          <div className="bg-black/20 p-2 rounded border border-yellow-600/50">
            <div className="font-bold text-yellow-200 mb-1">🎴 KI-Hand ({aiHandCards.length} Karten):</div>

            {colorCards.length > 0 && (
              <div className="mb-1">
                <span className="text-blue-300 font-semibold">Farben ({colorCards.length}):</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {colorCards.map((card, idx) => (
                    <span
                      key={idx}
                      className="bg-blue-800/50 px-1 py-0.5 rounded text-xs border border-blue-500/50"
                      title={`${card.name} - ${card.points || 0} Punkte`}
                    >
                      {card.name} ({card.points || 0}P)
                    </span>
                  ))}
                </div>
              </div>
            )}

            {specialCards.length > 0 && (
              <div>
                <span className="text-purple-300 font-semibold">Spezial ({specialCards.length}):</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {specialCards.map((card, idx) => {
                    const config = SPECIAL_CARD_TEST_CONFIG[card.name];
                    return (
                      <span
                        key={idx}
                        className={`px-1 py-0.5 rounded text-xs border ${
                          card.name === 'Verdoppler'
                            ? 'bg-green-800/50 border-green-500/50 text-green-200 font-bold'
                            : 'bg-purple-800/50 border-purple-500/50'
                        }`}
                        title={card.description || card.name}
                      >
                        {config?.emoji || '🎴'} {card.name}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}

            {aiHandCards.length === 0 && (
              <div className="text-gray-400 italic">Keine Karten auf der Hand</div>
            )}
          </div>
        )}

        {/* General Actions */}
        <div className="bg-gray-900/30 p-2 rounded border border-gray-600/50">
          <div className="font-bold text-gray-200 mb-2">⚙️ ALLGEMEINE AKTIONEN</div>
          <div className="flex flex-col gap-1">
            <button
              onClick={() => handleQuickAction('block_ai')}
              className="bg-orange-600 hover:bg-orange-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🚫 KI blockieren (Schlangengrube-Test)
            </button>
            <button
              onClick={() => handleQuickAction('block_human')}
              className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🕳️ Spieler blockieren (Test)
            </button>
            <button
              onClick={() => handleQuickAction('force_fallback')}
              className="bg-purple-600 hover:bg-purple-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🔄 Fallback erzwingen (30s)
            </button>
            <button
              onClick={() => handleQuickAction('dump_state')}
              className="bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-xs transition-colors"
            >
              📊 State dumpen
            </button>
          </div>
        </div>

        {/* Log Management */}
        <div className="bg-indigo-900/30 p-2 rounded border border-indigo-600/50">
          <div className="font-bold text-indigo-200 mb-2">📝 LOG-VERWALTUNG</div>
          <div className="text-xs text-indigo-300 mb-2">
            Logs: {getLogStats().totalEntries}/{getLogStats().maxEntries} Einträge
          </div>
          <div className="flex flex-col gap-1">
            <button
              onClick={downloadLogFile}
              className="bg-indigo-600 hover:bg-indigo-700 px-2 py-1 rounded text-xs transition-colors"
            >
              💾 Debug-Log herunterladen
            </button>
            <button
              onClick={() => {
                saveLogsToStorage();
                console.log('Logs in localStorage gespeichert - können jetzt analysiert werden');
              }}
              className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
            >
              💿 Logs in Browser speichern
            </button>
            <button
              onClick={() => {
                const stored = loadLogsFromStorage();
                if (stored) {
                  console.log('Gespeicherte Logs gefunden:', stored.stats);
                  console.log('Log-Einträge:', stored.entries);
                } else {
                  console.log('Keine gespeicherten Logs gefunden');
                }
              }}
              className="bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
            >
              📖 Gespeicherte Logs anzeigen
            </button>
            <button
              onClick={() => {
                const logContent = logEntries.join('\n');
                navigator.clipboard.writeText(logContent).then(() => {
                  console.log('✅ Logs in Zwischenablage kopiert - können jetzt in logs/debug.log eingefügt werden');
                  alert('Logs in Zwischenablage kopiert!\nFügen Sie sie in logs/debug.log ein.');
                }).catch(err => {
                  console.error('Fehler beim Kopieren:', err);
                });
              }}
              className="bg-purple-600 hover:bg-purple-700 px-2 py-1 rounded text-xs transition-colors"
            >
              📋 Logs kopieren (für Datei)
            </button>
            <button
              onClick={showSchlangengrubeAnalysis}
              className="bg-yellow-600 hover:bg-yellow-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🔍 Schlangengrube-Analyse
            </button>
            <button
              onClick={clearLogs}
              className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🗑️ Logs löschen
            </button>
          </div>
        </div>

        {/* Testing Instructions */}
        {isExpanded && (
          <div className="bg-green-900/20 p-2 rounded border border-green-600/50">
            <div className="font-bold text-green-200 mb-1">📋 TEST-ANLEITUNG</div>
            <div className="text-xs text-green-300 space-y-1">
              <div>1. Sonderkarte aus Dropdown wählen</div>
              <div>2. "KI [Karte] geben" klicken</div>
              <div>3. Fallback-Modus wird automatisch aktiviert</div>
              <div>4. KI-Zug abwarten und Verhalten beobachten</div>
              <div>5. Debug-Logs in Konsole prüfen</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};