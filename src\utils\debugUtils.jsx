import React, { useState } from 'react';
import { SPECIAL_CARDS_NAMES } from '@/utils/gameRules';

// Debug-Utilities für Entwicklungsmodus
export const isDebugMode = () => {
  return import.meta.env.VITE_DEBUG_MODE === 'true' || import.meta.env.DEV;
};

export const debugLog = (message, ...args) => {
  if (isDebugMode()) {
    console.log(message, ...args);
  }
};

export const debugWarn = (message, ...args) => {
  if (isDebugMode()) {
    console.warn(message, ...args);
  }
};

export const debugError = (message, ...args) => {
  if (isDebugMode()) {
    console.error(message, ...args);
  }
};

// Debug-Funktionen für Spielzustand
export const dumpGameState = (gameState, label = 'GameState') => {
  if (isDebugMode()) {
    console.group(`🐍 [DEBUG] ${label}`);
    console.log('Current Player:', gameState.currentPlayerId);
    console.log('Turn Number:', gameState.turnNumber);
    console.log('Player Hand:', gameState.playerHand?.length || 0, 'cards');
    console.log('AI Opponents:', gameState.aiOpponentsData?.map(ai => ({
      id: ai.id,
      handSize: ai.hand?.length || 0,
      isBlocked: ai.isBlockedByGrube,
      colorCardsPlayed: ai.colorCardsPlayedThisTurn || 0,
      specialCardsPlayed: ai.specialCardsPlayedThisTurn || 0
    })));
    console.log('Deck size:', gameState.deck?.length || 0);
    console.log('Discard pile:', gameState.discardPile?.length || 0);
    console.groupEnd();
  }
};

// Special Card Testing Configuration
export const SPECIAL_CARD_TEST_CONFIG = {
  [SPECIAL_CARDS_NAMES.DOUBLER]: {
    name: 'Verdoppler',
    emoji: '⚡',
    description: 'Erlaubt bis zu 3 Karten flexibel (Farbe/Spezial)',
    priority: 'HIGH',
    testNotes: 'Sollte zuerst gespielt werden, dann 2 weitere Karten'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_PIT]: {
    name: 'Schlangengrube',
    emoji: '🕳️',
    description: 'Blockiert Gegner für eine Runde',
    priority: 'HIGH',
    testNotes: 'Sollte Gegner auswählen und blockieren'
  },
  [SPECIAL_CARDS_NAMES.COLOR_THIEF]: {
    name: 'Farbendieb',
    emoji: '🎭',
    description: 'Stiehlt Karte von Gegnerschlange',
    priority: 'MEDIUM',
    testNotes: 'Sollte beste Karte von Gegner stehlen'
  },
  [SPECIAL_CARDS_NAMES.COLOR_FUSION]: {
    name: 'Farbenfusion',
    emoji: '🔗',
    description: 'Fusioniert zwei benachbarte Karten gleicher Farbe',
    priority: 'MEDIUM',
    testNotes: 'Benötigt 2+ Karten gleicher Farbe in Schlange'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_MOLTING]: {
    name: 'Schlangenhäutung',
    emoji: '🐍',
    description: 'Ordnet Schlangenabschnitt neu',
    priority: 'MEDIUM',
    testNotes: 'Benötigt 3+ Karten in Schlange'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_EATER]: {
    name: 'Schlangenfrass',
    emoji: '🍽️',
    description: 'Entfernt Karten von eigener/gegnerischer Schlange',
    priority: 'MEDIUM',
    testNotes: 'Sollte strategisch Karten entfernen'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_BLOCKADE]: {
    name: 'Schlangenblockade',
    emoji: '🚧',
    description: 'Fügt Blockade zur Schlange hinzu',
    priority: 'LOW',
    testNotes: 'Negative Karte, sollte vermieden werden'
  },
  [SPECIAL_CARDS_NAMES.COLOR_PROTECTION]: {
    name: 'Farbenschutz',
    emoji: '🛡️',
    description: 'Schutz vor nächster negativer Sonderkarte',
    priority: 'MEDIUM',
    testNotes: 'Defensiv, sollte bei Bedrohung gespielt werden'
  },
  [SPECIAL_CARDS_NAMES.RAINBOW_SNAKE]: {
    name: 'Regenbogenschlange',
    emoji: '🌈',
    description: 'Flexibler Joker für Schlange',
    priority: 'HIGH',
    testNotes: 'Sollte strategisch platziert werden'
  },
  [SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK]: {
    name: 'Schlangenkorb',
    emoji: '🧺',
    description: 'Verschiedene Glücksoptionen',
    priority: 'MEDIUM',
    testNotes: 'Sollte beste Option wählen'
  }
};

// Debug-Panel Komponente für erweiterte Informationen
export const DebugPanel = ({ gameState, onAction }) => {
  const [selectedSpecialCard, setSelectedSpecialCard] = useState(SPECIAL_CARDS_NAMES.SNAKE_PIT);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isDebugMode()) return null;

  const handleQuickAction = (action, params = {}) => {
    debugLog(`[QUICK-ACTION] ${action}`, params);
    if (onAction) onAction(action, params);
  };

  const aiOpponent = gameState.aiOpponentsData?.[0] || {};
  const hasVerdoppler = aiOpponent.hand?.some(c => c.name === 'Verdoppler') || false;
  const verdopplerCount = aiOpponent.hand?.filter(c => c.name === 'Verdoppler').length || 0;

  // Detaillierte Kartenauflistung
  const aiHandCards = aiOpponent.hand || [];
  const colorCards = aiHandCards.filter(c => c.type === 'color' || c.type === 'COLOR');
  const specialCards = aiHandCards.filter(c => c.type === 'special' || c.type === 'SPECIAL');

  // Special Card Testing
  const selectedCardConfig = SPECIAL_CARD_TEST_CONFIG[selectedSpecialCard];
  const hasSelectedCard = aiOpponent.hand?.some(c => c.name === selectedSpecialCard) || false;
  const selectedCardCount = aiOpponent.hand?.filter(c => c.name === selectedSpecialCard).length || 0;

  return (
    <div className="fixed top-32 right-4 bg-red-900/90 backdrop-blur-md text-white p-3 rounded-lg border border-red-600 z-50 min-w-[320px] max-w-[420px]">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-bold text-red-200">🔧 DEBUG PANEL</h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-xs bg-red-700 hover:bg-red-600 px-2 py-1 rounded transition-colors"
        >
          {isExpanded ? '▼' : '▶'} {isExpanded ? 'Weniger' : 'Mehr'}
        </button>
      </div>

      <div className="space-y-2 text-xs">
        {/* Basic Game State */}
        <div className="bg-black/30 p-2 rounded">
          <div><strong>Deck:</strong> {gameState.deck?.length || 0} Karten</div>
          <div><strong>Abwurf:</strong> {gameState.discardPile?.length || 0} Karten</div>
          <div><strong>Phase:</strong> {gameState.gamePhase}</div>
          <div><strong>KI Verdoppler:</strong> {verdopplerCount} in Hand ({hasVerdoppler ? '✅' : '❌'})</div>
        </div>

        {/* Special Card Testing Section */}
        <div className="bg-blue-900/30 p-2 rounded border border-blue-600/50">
          <div className="font-bold text-blue-200 mb-2">🎴 SONDERKARTEN-TEST</div>

          {/* Special Card Selector */}
          <div className="mb-2">
            <label className="block text-xs text-blue-300 mb-1">Karte auswählen:</label>
            <select
              value={selectedSpecialCard}
              onChange={(e) => setSelectedSpecialCard(e.target.value)}
              className="w-full bg-black/50 border border-blue-500/50 rounded px-2 py-1 text-xs text-white"
            >
              {Object.entries(SPECIAL_CARD_TEST_CONFIG).map(([cardName, config]) => (
                <option key={cardName} value={cardName}>
                  {config.emoji} {config.name} ({config.priority})
                </option>
              ))}
            </select>
          </div>

          {/* Selected Card Info */}
          {selectedCardConfig && (
            <div className="bg-black/30 p-2 rounded mb-2 border border-gray-600/50">
              <div className="font-semibold text-yellow-300">
                {selectedCardConfig.emoji} {selectedCardConfig.name}
              </div>
              <div className="text-gray-300 text-xs mt-1">{selectedCardConfig.description}</div>
              <div className="text-blue-300 text-xs mt-1 italic">{selectedCardConfig.testNotes}</div>
              <div className="mt-1">
                <span className={`text-xs px-1 py-0.5 rounded ${
                  selectedCardConfig.priority === 'HIGH' ? 'bg-red-700 text-red-200' :
                  selectedCardConfig.priority === 'MEDIUM' ? 'bg-yellow-700 text-yellow-200' :
                  'bg-gray-700 text-gray-200'
                }`}>
                  {selectedCardConfig.priority} PRIORITY
                </span>
              </div>
              <div className="mt-1 text-xs">
                <strong>KI hat:</strong> {selectedCardCount} x {selectedCardConfig.name} ({hasSelectedCard ? '✅' : '❌'})
              </div>
            </div>
          )}

          {/* Test Actions */}
          <div className="flex flex-col gap-1">
            <button
              onClick={() => handleQuickAction('give_ai_special_card', { cardName: selectedSpecialCard })}
              className="bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
            >
              {selectedCardConfig?.emoji} KI {selectedCardConfig?.name} geben
            </button>
            <button
              onClick={() => handleQuickAction('give_ai_verdoppler')}
              className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
            >
              ⚡ KI Verdoppler geben (Legacy)
            </button>
          </div>
        </div>

        {/* Expanded AI Hand Details */}
        {isExpanded && (
          <div className="bg-black/20 p-2 rounded border border-yellow-600/50">
            <div className="font-bold text-yellow-200 mb-1">🎴 KI-Hand ({aiHandCards.length} Karten):</div>

            {colorCards.length > 0 && (
              <div className="mb-1">
                <span className="text-blue-300 font-semibold">Farben ({colorCards.length}):</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {colorCards.map((card, idx) => (
                    <span
                      key={idx}
                      className="bg-blue-800/50 px-1 py-0.5 rounded text-xs border border-blue-500/50"
                      title={`${card.name} - ${card.points || 0} Punkte`}
                    >
                      {card.name} ({card.points || 0}P)
                    </span>
                  ))}
                </div>
              </div>
            )}

            {specialCards.length > 0 && (
              <div>
                <span className="text-purple-300 font-semibold">Spezial ({specialCards.length}):</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {specialCards.map((card, idx) => {
                    const config = SPECIAL_CARD_TEST_CONFIG[card.name];
                    return (
                      <span
                        key={idx}
                        className={`px-1 py-0.5 rounded text-xs border ${
                          card.name === 'Verdoppler'
                            ? 'bg-green-800/50 border-green-500/50 text-green-200 font-bold'
                            : 'bg-purple-800/50 border-purple-500/50'
                        }`}
                        title={card.description || card.name}
                      >
                        {config?.emoji || '🎴'} {card.name}
                      </span>
                    );
                  })}
                </div>
              </div>
            )}

            {aiHandCards.length === 0 && (
              <div className="text-gray-400 italic">Keine Karten auf der Hand</div>
            )}
          </div>
        )}

        {/* General Actions */}
        <div className="bg-gray-900/30 p-2 rounded border border-gray-600/50">
          <div className="font-bold text-gray-200 mb-2">⚙️ ALLGEMEINE AKTIONEN</div>
          <div className="flex flex-col gap-1">
            <button
              onClick={() => handleQuickAction('block_ai')}
              className="bg-orange-600 hover:bg-orange-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🚫 KI blockieren (Schlangengrube-Test)
            </button>
            <button
              onClick={() => handleQuickAction('force_fallback')}
              className="bg-purple-600 hover:bg-purple-700 px-2 py-1 rounded text-xs transition-colors"
            >
              🔄 Fallback erzwingen (30s)
            </button>
            <button
              onClick={() => handleQuickAction('dump_state')}
              className="bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-xs transition-colors"
            >
              📊 State dumpen
            </button>
          </div>
        </div>

        {/* Testing Instructions */}
        {isExpanded && (
          <div className="bg-green-900/20 p-2 rounded border border-green-600/50">
            <div className="font-bold text-green-200 mb-1">📋 TEST-ANLEITUNG</div>
            <div className="text-xs text-green-300 space-y-1">
              <div>1. Sonderkarte aus Dropdown wählen</div>
              <div>2. "KI [Karte] geben" klicken</div>
              <div>3. Fallback-Modus wird automatisch aktiviert</div>
              <div>4. KI-Zug abwarten und Verhalten beobachten</div>
              <div>5. Debug-Logs in Konsole prüfen</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};