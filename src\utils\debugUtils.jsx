import React from 'react';

// Debug-Utilities für Entwicklungsmodus
export const isDebugMode = () => {
  return import.meta.env.VITE_DEBUG_MODE === 'true' || import.meta.env.DEV;
};

export const debugLog = (message, ...args) => {
  if (isDebugMode()) {
    console.log(message, ...args);
  }
};

export const debugWarn = (message, ...args) => {
  if (isDebugMode()) {
    console.warn(message, ...args);
  }
};

export const debugError = (message, ...args) => {
  if (isDebugMode()) {
    console.error(message, ...args);
  }
};

// Debug-Funktionen für Spielzustand
export const dumpGameState = (gameState, label = 'GameState') => {
  if (isDebugMode()) {
    console.group(`🐍 [DEBUG] ${label}`);
    console.log('Current Player:', gameState.currentPlayerId);
    console.log('Turn Number:', gameState.turnNumber);
    console.log('Player Hand:', gameState.playerHand?.length || 0, 'cards');
    console.log('AI Opponents:', gameState.aiOpponentsData?.map(ai => ({
      id: ai.id,
      handSize: ai.hand?.length || 0,
      isBlocked: ai.isBlockedByGrube,
      colorCardsPlayed: ai.colorCardsPlayedThisTurn || 0,
      specialCardsPlayed: ai.specialCardsPlayedThisTurn || 0
    })));
    console.log('Deck size:', gameState.deck?.length || 0);
    console.log('Discard pile:', gameState.discardPile?.length || 0);
    console.groupEnd();
  }
};

// Debug-Panel Komponente für erweiterte Informationen
export const DebugPanel = ({ gameState, onAction }) => {
  if (!isDebugMode()) return null;

  const handleQuickAction = (action) => {
    debugLog(`[QUICK-ACTION] ${action}`);
    if (onAction) onAction(action);
  };

  const aiOpponent = gameState.aiOpponentsData?.[0] || {};
  const hasVerdoppler = aiOpponent.hand?.some(c => c.name === 'Verdoppler') || false;
  const verdopplerCount = aiOpponent.hand?.filter(c => c.name === 'Verdoppler').length || 0;
  
  // Detaillierte Kartenauflistung
  const aiHandCards = aiOpponent.hand || [];
  const colorCards = aiHandCards.filter(c => c.type === 'color' || c.type === 'COLOR');
  const specialCards = aiHandCards.filter(c => c.type === 'special' || c.type === 'SPECIAL');

  return (
    <div className="fixed top-32 right-4 bg-red-900/90 backdrop-blur-md text-white p-3 rounded-lg border border-red-600 z-50 min-w-[320px] max-w-[400px]">
      <h3 className="text-sm font-bold mb-2 text-red-200">🔧 DEBUG PANEL</h3>
      
      <div className="space-y-2 text-xs">
        <div className="bg-black/30 p-2 rounded">
          <div><strong>Deck:</strong> {gameState.deck?.length || 0} Karten</div>
          <div><strong>Abwurf:</strong> {gameState.discardPile?.length || 0} Karten</div>
          <div><strong>Phase:</strong> {gameState.gamePhase}</div>
          <div><strong>KI Verdoppler:</strong> {verdopplerCount} in Hand ({hasVerdoppler ? '✅' : '❌'})</div>
        </div>
        
        {/* NEUE SEKTION: Detaillierte KI-Hand */}
        <div className="bg-black/20 p-2 rounded border border-yellow-600/50">
          <div className="font-bold text-yellow-200 mb-1">🎴 KI-Hand ({aiHandCards.length} Karten):</div>
          
          {colorCards.length > 0 && (
            <div className="mb-1">
              <span className="text-blue-300 font-semibold">Farben ({colorCards.length}):</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {colorCards.map((card, idx) => (
                  <span 
                    key={idx}
                    className="bg-blue-800/50 px-1 py-0.5 rounded text-xs border border-blue-500/50"
                    title={`${card.name} - ${card.points || 0} Punkte`}
                  >
                    {card.name} ({card.points || 0}P)
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {specialCards.length > 0 && (
            <div>
              <span className="text-purple-300 font-semibold">Spezial ({specialCards.length}):</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {specialCards.map((card, idx) => (
                  <span 
                    key={idx}
                    className={`px-1 py-0.5 rounded text-xs border ${
                      card.name === 'Verdoppler' 
                        ? 'bg-green-800/50 border-green-500/50 text-green-200 font-bold' 
                        : 'bg-purple-800/50 border-purple-500/50'
                    }`}
                    title={card.description || card.name}
                  >
                    {card.name === 'Verdoppler' ? '⚡' : '🎴'} {card.name}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {aiHandCards.length === 0 && (
            <div className="text-gray-400 italic">Keine Karten auf der Hand</div>
          )}
        </div>
        
        <div className="flex flex-col gap-1">
          <button 
            onClick={() => handleQuickAction('give_ai_verdoppler')}
            className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
          >
            🎯 KI Verdoppler geben
          </button>
          <button 
            onClick={() => handleQuickAction('block_ai')}
            className="bg-orange-600 hover:bg-orange-700 px-2 py-1 rounded text-xs transition-colors"
          >
            🚫 KI blockieren
          </button>
          <button 
            onClick={() => handleQuickAction('force_fallback')}
            className="bg-purple-600 hover:bg-purple-700 px-2 py-1 rounded text-xs transition-colors"
          >
            🔄 Fallback erzwingen
          </button>
          <button 
            onClick={() => handleQuickAction('dump_state')}
            className="bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-xs transition-colors"
          >
            📊 State dumpen
          </button>
        </div>
      </div>
    </div>
  );
}; 