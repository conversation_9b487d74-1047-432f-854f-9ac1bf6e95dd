import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Play, Settings, Users, Cpu, ArrowLeft } from 'lucide-react';
import { motion } from 'framer-motion';

const GameConfig = () => {
  const [aiOpponents, setAiOpponents] = React.useState(1);
  const [selectedModel, setSelectedModel] = React.useState("Gemini 2.5 Pro");
  const navigate = useNavigate();

  const models = [
    { id: "gemini", name: "Gemini 2.5 Pro", difficulty: "Mittel", icon: "🤖" },
    { id: "claude", name: "Claude 3.7 Sonnet", difficulty: "Schwer", icon: "🧠" },
    { id: "deepseek", name: "Deepseek Coder", difficulty: "Variabel", icon: "💡" }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    // Hier würden die Konfigurationen gespeichert oder an den nächsten State übergeben
    console.log("Spiel konfiguriert:", { aiOpponents, selectedModel });
    // Speichern der Konfiguration im localStorage für den Zugriff im GameBoard
    localStorage.setItem('gameConfig', JSON.stringify({ aiOpponents, selectedModel }));
    navigate('/game');
  };
  
  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 p-4">
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
      >
        <Card className="w-full max-w-md bg-slate-800/80 backdrop-blur-md border-slate-700 text-white shadow-2xl">
          <CardHeader className="text-center">
            <Settings className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-purple-400 mb-3 sm:mb-4" />
            <CardTitle className="text-2xl sm:text-3xl font-bold text-purple-300">Spieleinstellungen</CardTitle>
            <CardDescription className="text-slate-400 text-sm sm:text-base">Passe dein strategisches Erlebnis an.</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-5 sm:space-y-6">
              <motion.div initial={{ opacity:0, x: -20 }} animate={{ opacity:1, x: 0}} transition={{ delay: 0.1 }}>
                <Label htmlFor="ai-opponents" className="text-md sm:text-lg flex items-center text-slate-200 mb-1.5 sm:mb-2">
                  <Users className="mr-2 h-5 w-5 text-purple-400" />
                  Anzahl KI-Gegner
                </Label>
                <div className="flex items-center space-x-2 sm:space-x-3">
                  {[1, 2, 3].map((num) => (
                    <Button
                      key={num}
                      type="button"
                      variant={aiOpponents === num ? "default" : "outline"}
                      onClick={() => setAiOpponents(num)}
                      className={`flex-1 py-2.5 sm:py-3 text-sm sm:text-base transition-all duration-200 ${aiOpponents === num ? 'bg-purple-600 hover:bg-purple-700 ring-2 ring-purple-400' : 'text-purple-300 border-purple-500 hover:bg-purple-500/30 hover:text-white'}`}
                    >
                      {num} Spieler
                    </Button>
                  ))}
                </div>
              </motion.div>
              <motion.div initial={{ opacity:0, x: -20 }} animate={{ opacity:1, x: 0}} transition={{ delay: 0.2 }}>
                <Label htmlFor="ai-model" className="text-md sm:text-lg flex items-center text-slate-200 mb-1.5 sm:mb-2">
                  <Cpu className="mr-2 h-5 w-5 text-purple-400" />
                  KI-Modell für Gegner 1
                </Label>
                <select
                  id="ai-model"
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="w-full p-2.5 sm:p-3 rounded-md bg-slate-700 border border-slate-600 text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                >
                  {models.map((model) => (
                    <option key={model.id} value={model.name}>
                      {model.icon} {model.name} ({model.difficulty})
                    </option>
                  ))}
                </select>
                 {/* Hier könnten weitere Dropdowns für mehr KI-Gegner hinzugefügt werden, falls aiOpponents > 1 */}
              </motion.div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-3 pt-4">
              <Button type="submit" className="w-full text-md sm:text-lg py-2.5 sm:py-3 bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg focus:ring-4 focus:ring-green-300">
                <Play className="mr-2 h-5 w-5 sm:h-6 sm:w-6" />
                Spiel starten
              </Button>
            </CardFooter>
          </form>
        </Card>
      </motion.div>
      <motion.div initial={{ opacity:0 }} animate={{ opacity:1 }} transition={{ delay: 0.5 }}>
        <Button asChild variant="link" className="mt-6 text-sm text-slate-400 hover:text-purple-300 transition-colors">
          <Link to="/">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Zurück zum Hauptmenü
          </Link>
        </Button>
      </motion.div>
    </div>
  );
};

export default GameConfig;