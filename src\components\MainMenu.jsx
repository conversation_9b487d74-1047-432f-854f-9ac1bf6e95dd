import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Play, BookOpen, BarChart3, User, Settings, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import Footer from '@/components/shared/Footer';

const MainMenu = () => {
  const navigate = useNavigate();
  const { user, signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    navigate('/signin');
  };

  const menuItems = [
    { name: '<PERSON><PERSON><PERSON>', icon: <Play className="mr-3 h-6 w-6" />, path: '/config', color: 'bg-primary-gold hover:bg-primary-gold/90 text-slate-900', shadow: 'shadow-lg shadow-primary-gold/30' },
    { name: 'Tutorial', icon: <BookOpen className="mr-3 h-6 w-6" />, path: '/tutorial', color: 'bg-primary hover:bg-primary/90 text-primary-foreground', shadow: 'shadow-lg shadow-primary/30' },
    { name: '<PERSON>enliste', icon: <BarChart3 className="mr-3 h-6 w-6" />, path: '/leaderboard', color: 'bg-primary hover:bg-primary/90 text-primary-foreground', shadow: 'shadow-lg shadow-primary/30' },
    { name: 'Profil', icon: <User className="mr-3 h-6 w-6" />, path: '/profile', color: 'bg-secondary hover:bg-secondary/90 text-secondary-foreground', shadow: 'shadow-lg shadow-secondary/30' },
    { name: 'Einstellungen', icon: <Settings className="mr-3 h-6 w-6" />, path: '/settings', color: 'bg-secondary hover:bg-secondary/90 text-secondary-foreground', shadow: 'shadow-lg shadow-secondary/30' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-primary-dark-green via-slate-900 to-primary-burgundy p-6 text-foreground">
      <motion.div
        className="w-full max-w-md bg-slate-800/70 backdrop-blur-md p-8 rounded-xl shadow-2xl"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <motion.h1 
          className="text-5xl font-heading text-center mb-4 text-primary-gold"
          initial={{ y: -30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 120 }}
        >
          Hauptmenü
        </motion.h1>
        {user && (
          <motion.p 
            className="text-center text-slate-300 mb-8 text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            Willkommen zurück, <span className="font-semibold text-primary-gold">{user.user_metadata?.username || user.email}</span>!
          </motion.p>
        )}

        <motion.ul
          className="space-y-4 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {menuItems.map((item) => (
            <motion.li key={item.name} variants={itemVariants}>
              <Link to={item.path}>
                <Button
                  variant="default"
                  className={`w-full justify-start text-lg py-6 rounded-lg transition-all duration-300 transform hover:scale-105 ${item.color} ${item.shadow}`}
                >
                  {item.icon}
                  {item.name}
                </Button>
              </Link>
            </motion.li>
          ))}
        </motion.ul>

        <motion.div variants={itemVariants}>
          <Button
            onClick={handleSignOut}
            variant="outline"
            className="w-full justify-start text-lg py-6 rounded-lg border-slate-500 text-slate-300 hover:bg-slate-700 hover:text-white transition-all duration-300 transform hover:scale-105"
          >
            <LogOut className="mr-3 h-6 w-6" />
            Abmelden
          </Button>
        </motion.div>
      </motion.div>
      <Footer className="text-slate-400 mt-12" />
    </div>
  );
};

export default MainMenu;
