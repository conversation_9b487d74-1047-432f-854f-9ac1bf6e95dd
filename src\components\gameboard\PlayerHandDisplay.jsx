import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Card from '@/components/Card';

const PlayerHandDisplay = ({ cards, onCardSelect, selectedCardId, className, onDragStart }) => {
  if (!cards || cards.length === 0) {
    return <p className="text-slate-400 italic text-center py-4"><PERSON><PERSON> auf der Hand.</p>;
  }
  return (
    <div className={`flex space-x-1 sm:space-x-2 p-2 overflow-x-auto justify-center items-end min-h-[150px] ${className}`}>
      <AnimatePresence>
        {cards.map((card, index) => (
          <motion.div
            key={card.id}
            layoutId={`hand-${card.id}`}
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 50, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 25, delay: index * 0.05 }}
            onClick={() => onCardSelect && onCardSelect(card)}
            className={`cursor-pointer transform transition-all duration-200 ${selectedCardId === card.id ? 'ring-4 ring-primary-gold scale-105 shadow-xl -translate-y-2' : 'hover:scale-105 hover:-translate-y-1'}`}
          >
            <Card 
              id={card.id} 
              text={card.name} 
              type={card.color || card.type}
              points={card.points}
              description={card.description}
              onDragStart={onDragStart}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default PlayerHandDisplay;
