import React from 'react';
import { motion } from 'framer-motion';
import TaskCardDisplay from '@/components/gameboard/TaskCardDisplay';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CheckSquare, Lock, HelpCircle, ShieldCheck, Gift } from 'lucide-react';

const TaskPanel = ({ openTasks, secretTask, playerTasks, auxCards, onCompleteTask, activeSnake, className }) => {
  const containerVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { staggerChildren: 0.1, delayChildren: 0.3, type: "spring", stiffness: 100 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const completedPlayerTaskIds = playerTasks.map(pt => pt.id);

  return (
    <motion.div 
      className={`bg-slate-800/60 backdrop-blur-sm p-2 sm:p-3 rounded-xl shadow-xl border border-slate-700/60 flex flex-col ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <h2 className="text-lg sm:text-xl font-heading text-primary-gold mb-2 sm:mb-3">Aufgaben & Extras</h2>
      
      <ScrollArea className="flex-grow pr-2 sm:pr-3 -mr-2 sm:-mr-3">
        <div className="space-y-2 sm:space-y-3">
          <motion.div variants={itemVariants}>
            <h3 className="text-sm sm:text-base font-semibold text-sky-300 mb-1 flex items-center">
              <CheckSquare className="h-4 w-4 mr-2 text-sky-400" /> Offene Aufgaben
            </h3>
            {openTasks.map(task => (
              <TaskCardDisplay 
                key={task.id} 
                task={task} 
                onCompleteTask={onCompleteTask} 
                isCompleted={completedPlayerTaskIds.includes(task.id)}
                activeSnake={activeSnake}
              />
            ))}
            {openTasks.length === 0 && <p className="text-xs text-slate-400 italic">Keine offenen Aufgaben mehr.</p>}
          </motion.div>

          {secretTask && (
            <motion.div variants={itemVariants}>
              <h3 className="text-sm sm:text-base font-semibold text-violet-300 mb-1 flex items-center">
                <Lock className="h-4 w-4 mr-2 text-violet-400" /> Geheime Aufgabe
              </h3>
              <TaskCardDisplay 
                task={secretTask} 
                onCompleteTask={onCompleteTask} 
                isCompleted={secretTask.isCompleted}
                isSecret={true}
                activeSnake={activeSnake}
              />
            </motion.div>
          )}

          {auxCards && (
            <>
              <motion.div variants={itemVariants}>
                <h3 className="text-sm sm:text-base font-semibold text-emerald-300 mb-1 flex items-center">
                  <ShieldCheck className="h-4 w-4 mr-2 text-emerald-400" /> Comeback-Karte
                </h3>
                <TaskCardDisplay 
                  task={{...auxCards.comebackCard, type: 'comeback_display'}} 
                  isCompleted={auxCards.comebackCard.used}
                  isAux={true}
                />
              </motion.div>
              <motion.div variants={itemVariants}>
                <h3 className="text-sm sm:text-base font-semibold text-amber-300 mb-1 flex items-center">
                  <Gift className="h-4 w-4 mr-2 text-amber-400" /> Risiko-Belohnung
                </h3>
                {auxCards.riskRewardCards.map((card, index) => (
                   <TaskCardDisplay 
                    key={`risk-${index}`}
                    task={{...card, type: 'risk_reward_display'}} 
                    isCompleted={card.used}
                    isAux={true}
                  />
                ))}
              </motion.div>
            </>
          )}

          {playerTasks && playerTasks.length > 0 && (
            <motion.div variants={itemVariants}>
              <h3 className="text-sm sm:text-base font-semibold text-green-400 mb-1 flex items-center">
                <CheckSquare className="h-4 w-4 mr-2 text-green-500" /> Erfüllte Aufgaben
              </h3>
              {playerTasks.map(task => (
                <TaskCardDisplay key={`completed-${task.id}`} task={task} isCompleted={true} />
              ))}
            </motion.div>
          )}
        </div>
      </ScrollArea>
    </motion.div>
  );
};

export default TaskPanel;