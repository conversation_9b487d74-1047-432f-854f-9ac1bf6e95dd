export const CARD_TYPES = {
  COLOR: 'color',
  SPECIAL: 'special',
  TASK_OPEN: 'task_open',
  TASK_SECRET: 'task_secret',
  COMEBACK: 'comeback',
  RISK_REWARD: 'risk_reward',
};

export const COLORS = {
  BLUE: 'Blau',
  RED: 'Rot',
  YELLOW: 'Gelb',
  VIOLET: '<PERSON><PERSON>',
  BROWN: '<PERSON>',
  GREEN: 'Grü<PERSON>',
  ALL: ['Blau', 'Rot', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>ü<PERSON>']
};

export const SPECIAL_CARDS_NAMES = {
  COLOR_PROTECTION: 'Farbenschutz',
  RAINBOW_SNAKE: 'Regenbogenschlange',
  SNAKE_EATER: 'Schlangenfrass',
  SNAKE_BLOCKADE: 'Schlangenblockade',
  COLOR_THIEF: 'Farbendieb',
  SNAKE_PIT: 'Schlang<PERSON>rube',
  COLOR_FUSION: 'Farbenfusion',
  DOUBLER: 'Verdoppler',
  SNAKE_MOLTING: 'Schlang<PERSON>h<PERSON>utung',
  SNAKE_BASKET_OF_LUCK: 'Schlangenkorb des Glücks',
};

const ALL_SPECIAL_CARD_NAMES_ARRAY = Object.values(SPECIAL_CARDS_NAMES);


export const createDeck = () => {
  let idCounter = 0;
  const deck = [];

  const addCards = (type, name, points, count, color = null) => {
    for (let i = 0; i < count; i++) {
      deck.push({ 
        id: `card-${idCounter++}`, 
        type, 
        name, 
        points, 
        color, 
        description: `${name}${color ? ` (${color})` : ''}${points ? ` - ${points}P` : ''}` 
      });
    }
  };

  addCards(CARD_TYPES.COLOR, COLORS.BLUE, 1, 15, COLORS.BLUE);
  addCards(CARD_TYPES.COLOR, COLORS.RED, 1, 15, COLORS.RED);
  addCards(CARD_TYPES.COLOR, COLORS.YELLOW, 1, 15, COLORS.YELLOW);
  addCards(CARD_TYPES.COLOR, COLORS.VIOLET, 2, 12, COLORS.VIOLET);
  addCards(CARD_TYPES.COLOR, COLORS.BROWN, 2, 12, COLORS.BROWN);
  addCards(CARD_TYPES.COLOR, COLORS.GREEN, 3, 9, COLORS.GREEN);

  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.COLOR_PROTECTION, 0, 4);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.RAINBOW_SNAKE, 0, 4);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.SNAKE_EATER, 0, 4);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.SNAKE_BLOCKADE, 0, 3);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.COLOR_THIEF, 0, 4);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.SNAKE_PIT, 0, 3);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.COLOR_FUSION, 0, 4);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.DOUBLER, 0, 4);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.SNAKE_MOLTING, 0, 3);
  addCards(CARD_TYPES.SPECIAL, SPECIAL_CARDS_NAMES.SNAKE_BASKET_OF_LUCK, 0, 1);
  
  return deck;
};

export const TASK_IDS = {
  OPEN_FARBVIELFALT: 'open-farbvielfalt',
  OPEN_FARBKOMBINATION: 'open-farbkombination',
  OPEN_SCHLANGENMEISTER: 'open-schlangenmeister',
  OPEN_SCHLANGENTANZ: 'open-schlangentanz',
  OPEN_FARBWECHSLER: 'open-farbwechsler',
  OPEN_SCHLANGENBÄNDIGER: 'open-schlangenbändiger',
  OPEN_SONDERKARTEN_SAMMLER: 'open-sonderkarten-sammler',
  OPEN_FARBHARMONIE: 'open-farbharmonie',
  SECRET_GELBER_SCHATZ: 'secret-gelber-schatz',
  SECRET_FUSIONSEXPERTE: 'secret-fusionsexperte',
  SECRET_VIELFALTSKÖNIG: 'secret-vielfaltskoenig',
  SECRET_GRÜNER_RIESE: 'secret-gruener-riese',
  SECRET_SCHLANGENBESCHWÖRER: 'secret-schlangenbeschwoerer',
  SECRET_FARBENPRACHT: 'secret-farbenpracht',
  SECRET_SYMMETRIEMEISTER: 'secret-symmetriemeister',
};


export const createTasks = () => {
  const openTasks = [
    { id: TASK_IDS.OPEN_FARBVIELFALT, type: CARD_TYPES.TASK_OPEN, description: "Farbvielfalt: Bilde eine Kette aus mindestens je einer Karte aller 6 Farben.", points: 9, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_FARBKOMBINATION, type: CARD_TYPES.TASK_OPEN, description: "Farbkombination: Habe 5 oder mehr Karten der gleichen Farbe nebeneinander in einer Schlange.", points: 5, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_SCHLANGENMEISTER, type: CARD_TYPES.TASK_OPEN, description: "Schlangenmeister: Habe mindestens 2 verschiedene Sonderkarten in deinen Schlangen und spiele insgesamt 4 Sonderkarten aus.", points: 4, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_SCHLANGENTANZ, type: CARD_TYPES.TASK_OPEN, description: "Schlangentanz: Bilde durch eine Schlangenhäutung zwei neue Dreiergruppen einer Farbe. Gilt nicht, wenn bereits eine Reihe von drei oder mehr Schlangen existiert.", points: 7, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_FARBWECHSLER, type: CARD_TYPES.TASK_OPEN, description: "Farbwechsler: Habe in einer Schlange mindestens 4 verschiedene Farben, die direkt aufeinander folgen. Es müssen nicht einzelne Karten aufeinanderfolgen. Es gelten auch Reihen.", points: 6, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_SCHLANGENBÄNDIGER, type: CARD_TYPES.TASK_OPEN, description: "Schlangenbändiger: Habe in einer Schlange ein sich wiederholendes Muster aus mindestens 3 verschiedenen Farben (z.B. R-B-G-R-B-G).", points: 7, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_SONDERKARTEN_SAMMLER, type: CARD_TYPES.TASK_OPEN, description: "Sonderkarten-Sammler: Spiele in einem Spiel mindestens fünf verschiedene Arten von Sonderkarten aus.", points: 3, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
    { id: TASK_IDS.OPEN_FARBHARMONIE, type: CARD_TYPES.TASK_OPEN, description: "Farbharmonie: Habe in jeder deiner Schlangen mindestens eine Dreiergruppe jeder Farbe.", points: 10, isCompleted: false, completedByPlayerId: null, completedInLastRound: false },
  ];

  const secretTasks = [
    { id: TASK_IDS.SECRET_GELBER_SCHATZ, type: CARD_TYPES.TASK_SECRET, description: "Gelber Schatz: Habe am Ende des Spiels mindestens 5 gelbe Karten in deinen Schlangen.", points: 6, isCompleted: false },
    { id: TASK_IDS.SECRET_FUSIONSEXPERTE, type: CARD_TYPES.TASK_SECRET, description: "Fusionsexperte: Habe am Ende des Spiels eine Schlange mit mindestens 2 Farbenfusionen.", points: 8, isCompleted: false },
    { id: TASK_IDS.SECRET_VIELFALTSKÖNIG, type: CARD_TYPES.TASK_SECRET, description: "Vielfaltskönig: Habe am Ende des Spiels in jeder deiner Schlangen mindestens 4 verschiedene Farben.", points: 9, isCompleted: false },
    { id: TASK_IDS.SECRET_GRÜNER_RIESE, type: CARD_TYPES.TASK_SECRET, description: "Grüner Riese: Habe am Ende des Spiels die längste ununterbrochene Kette grüner Karten (mindestens 3). Eine Regenbogenschlange oder eine Farbfusion gilt als unterbrochene Schlange.", points: 9, isCompleted: false },
    { id: TASK_IDS.SECRET_SCHLANGENBESCHWÖRER, type: CARD_TYPES.TASK_SECRET, description: "Schlangenbeschwörer: Habe am Ende des Spiels mindestens 4 Sonderkarten in deinen Schlangen.", points: 7, isCompleted: false },
    { id: TASK_IDS.SECRET_FARBENPRACHT, type: CARD_TYPES.TASK_SECRET, description: "Farbenpracht: Habe am Ende des Spiels von jeder Farbe mindestens zwei Karten in deinen beiden Schlangen.", points: 8, isCompleted: false },
    { id: TASK_IDS.SECRET_SYMMETRIEMEISTER, type: CARD_TYPES.TASK_SECRET, description: "Symmetriemeister: Habe am Ende des Spiels eine Schlange mit mindestens 8 Karten, bei der die erste Hälfte das Spiegelbild der zweiten Hälfte ist (z.B. B-R-B-G-G-B-R-B).", points: 10, isCompleted: false },
  ];
  
  return { openTasks, secretTasks };
};

export const createPlayerAuxCards = () => {
  return {
    comebackCard: { 
      id: 'comeback-player-1', 
      name: 'Comeback-Karte', 
      type: CARD_TYPES.COMEBACK, 
      description: 'Wenn deine längste Schlange mind. 5 Karten kürzer ist als die längste auf dem Tisch: Erhalte 2 zusätzliche Aktionen.',
      used: false 
    },
    riskRewardCards: [
      { id: 'risk-reward-player-1', name: 'Risiko-Belohnung 1', type: CARD_TYPES.RISK_REWARD, description: 'Wähle einen Bonus.', used: false },
      { id: 'risk-reward-player-2', name: 'Risiko-Belohnung 2', type: CARD_TYPES.RISK_REWARD, description: 'Wähle einen Bonus.', used: false }
    ]
  };
};

export const shuffleDeck = (deck) => {
  let newDeck = [...deck];
  for (let i = newDeck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newDeck[i], newDeck[j]] = [newDeck[j], newDeck[i]];
  }
  return newDeck;
};

export const dealCards = (deck, numPlayers, cardsPerPlayer) => {
  const hands = Array(numPlayers).fill(null).map(() => []);
  const remainingDeck = [...deck];
  for (let i = 0; i < cardsPerPlayer; i++) {
    for (let j = 0; j < numPlayers; j++) {
      if (remainingDeck.length > 0) {
        hands[j].push(remainingDeck.pop());
      }
    }
  }
  return { hands, remainingDeck };
};


const checkFarbvielfalt = (allPlayerSnakes) => {
  for (const snake of allPlayerSnakes) {
    const presentColors = new Set();
    snake.forEach(card => {
      if (card.type === CARD_TYPES.COLOR) presentColors.add(card.color);
    });
    if (COLORS.ALL.every(color => presentColors.has(color))) return true;
  }
  return false;
};

const checkFarbkombination = (allPlayerSnakes) => {
  for (const snake of allPlayerSnakes) {
    for (let i = 0; i <= snake.length - 5; i++) {
      const firstCardColor = snake[i].color;
      if (!firstCardColor || snake[i].type !== CARD_TYPES.COLOR) continue;
      let consecutive = true;
      for (let j = 1; j < 5; j++) {
        if (snake[i+j].type !== CARD_TYPES.COLOR || snake[i+j].color !== firstCardColor) {
          consecutive = false;
          break;
        }
      }
      if (consecutive) return true;
    }
  }
  return false;
};

const checkSchlangenmeister = (allPlayerSnakes, playedSpecialCardsHistory) => {
  const specialCardsInSnakes = new Set();
  allPlayerSnakes.flat().forEach(card => {
    if (card.type === CARD_TYPES.SPECIAL && ALL_SPECIAL_CARD_NAMES_ARRAY.includes(card.name)) {
      specialCardsInSnakes.add(card.name);
    }
  });
  return specialCardsInSnakes.size >= 2 && playedSpecialCardsHistory.length >= 4;
};

const checkSchlangentanz = (allPlayerSnakes, lastMoltingResult) => {
  if (!lastMoltingResult || !lastMoltingResult.formedNewGroups || lastMoltingResult.formedNewGroups.length < 2) return false;
  
  let groupsOfThreeCount = 0;
  for (const group of lastMoltingResult.formedNewGroups) {
    if (group.length >= 3) {
      groupsOfThreeCount++;
    }
  }
  return groupsOfThreeCount >= 2;
};

const checkFarbwechsler = (allPlayerSnakes) => {
  for (const snake of allPlayerSnakes) {
    if (snake.length < 4) continue;
    for (let i = 0; i <= snake.length - 4; i++) {
      const subSnake = snake.slice(i, i + 4);
      const colorsInSub = new Set();
      let validSub = true;
      for (const card of subSnake) {
        if (card.type !== CARD_TYPES.COLOR) {
          validSub = false;
          break;
        }
        colorsInSub.add(card.color);
      }
      if (validSub && colorsInSub.size === 4) return true;
    }
  }
  return false;
};

const checkSchlangenbaendiger = (allPlayerSnakes) => {
  for (const snake of allPlayerSnakes) {
    if (snake.length < 6) continue; 
    for (let patternLength = 3; patternLength <= Math.floor(snake.length / 2); patternLength++) {
      for (let i = 0; i <= snake.length - 2 * patternLength; i++) {
        const pattern = snake.slice(i, i + patternLength).map(c => c.type === CARD_TYPES.COLOR ? c.color : null);
        if (pattern.some(c => c === null)) continue;
        if (new Set(pattern).size < 3) continue; 

        const nextPattern = snake.slice(i + patternLength, i + 2 * patternLength).map(c => c.type === CARD_TYPES.COLOR ? c.color : null);
        if (pattern.every((val, index) => val === nextPattern[index])) return true;
      }
    }
  }
  return false;
};

const checkSonderkartenSammler = (playedSpecialCardsHistory) => {
  const uniquePlayedSpecialCards = new Set(playedSpecialCardsHistory.map(card => card.name));
  return uniquePlayedSpecialCards.size >= 5;
};

const checkFarbharmonie = (allPlayerSnakes) => {
  if (allPlayerSnakes.length === 0) return false;
  return allPlayerSnakes.every(snake => {
    return COLORS.ALL.every(color => {
      let count = 0;
      for (let i = 0; i < snake.length; i++) {
        if (snake[i].type === CARD_TYPES.COLOR && snake[i].color === color) {
          count = 1;
          for (let j = i + 1; j < snake.length; j++) {
            if (snake[j].type === CARD_TYPES.COLOR && snake[j].color === color) count++; else break;
          }
          if (count >= 3) return true;
        }
      }
      return false;
    });
  });
};


const checkGelberSchatz = (allPlayerSnakes) => {
  let yellowCount = 0;
  allPlayerSnakes.flat().forEach(card => {
    if (card.type === CARD_TYPES.COLOR && card.color === COLORS.YELLOW) yellowCount++;
  });
  return yellowCount >= 5;
};

const checkFusionsexperte = (allPlayerSnakes) => {
  for (const snake of allPlayerSnakes) {
    let fusionCount = 0;
    snake.forEach(card => {
      if (card.isFusion) fusionCount++;
    });
    if (fusionCount >= 2) return true;
  }
  return false;
};

const checkVielfaltskoenig = (allPlayerSnakes) => {
  if (allPlayerSnakes.length === 0) return false;
  return allPlayerSnakes.every(snake => {
    const colorsInSnake = new Set();
    snake.forEach(card => {
      if (card.type === CARD_TYPES.COLOR) colorsInSnake.add(card.color);
    });
    return colorsInSnake.size >= 4;
  });
};

const checkGruenerRiese = (allPlayerSnakes) => {
  let longestGreenChain = 0;
  allPlayerSnakes.forEach(snake => {
    let currentGreenChain = 0;
    snake.forEach(card => {
      if (card.type === CARD_TYPES.COLOR && card.color === COLORS.GREEN) {
        currentGreenChain++;
      } else {
        longestGreenChain = Math.max(longestGreenChain, currentGreenChain);
        currentGreenChain = 0;
      }
    });
    longestGreenChain = Math.max(longestGreenChain, currentGreenChain);
  });
  return longestGreenChain >= 3;
};

const checkSchlangenbeschwoerer = (allPlayerSnakes) => {
  let specialCount = 0;
  allPlayerSnakes.flat().forEach(card => {
    if (card.type === CARD_TYPES.SPECIAL && ALL_SPECIAL_CARD_NAMES_ARRAY.includes(card.name)) {
      specialCount++;
    }
  });
  return specialCount >= 4;
};

const checkFarbenpracht = (allPlayerSnakes) => {
  const colorCounts = {};
  COLORS.ALL.forEach(c => colorCounts[c] = 0);
  allPlayerSnakes.flat().forEach(card => {
    if (card.type === CARD_TYPES.COLOR && colorCounts[card.color] !== undefined) {
      colorCounts[card.color]++;
    }
  });
  return COLORS.ALL.every(color => colorCounts[color] >= 2);
};

const checkSymmetriemeister = (allPlayerSnakes) => {
  for (const snake of allPlayerSnakes) {
    if (snake.length >= 8 && snake.length % 2 === 0) {
      const halfLength = snake.length / 2;
      const firstHalf = snake.slice(0, halfLength);
      const secondHalf = snake.slice(halfLength).reverse();
      let isSymmetric = true;
      for (let i = 0; i < halfLength; i++) {
        if (firstHalf[i].type !== CARD_TYPES.COLOR || secondHalf[i].type !== CARD_TYPES.COLOR || firstHalf[i].color !== secondHalf[i].color) {
          isSymmetric = false;
          break;
        }
      }
      if (isSymmetric) return true;
    }
  }
  return false;
};

export const checkTaskCompletion = (allPlayerSnakes, task, gameStateContext) => {
  if (!task || !task.id) return false;

  switch (task.id) {
    case TASK_IDS.OPEN_FARBVIELFALT: return checkFarbvielfalt(allPlayerSnakes);
    case TASK_IDS.OPEN_FARBKOMBINATION: return checkFarbkombination(allPlayerSnakes);
    case TASK_IDS.OPEN_SCHLANGENMEISTER: return checkSchlangenmeister(allPlayerSnakes, gameStateContext?.playedSpecialCardsHistory || []);
    case TASK_IDS.OPEN_SCHLANGENTANZ: return checkSchlangentanz(allPlayerSnakes, gameStateContext?.lastMoltingResult || null);
    case TASK_IDS.OPEN_FARBWECHSLER: return checkFarbwechsler(allPlayerSnakes);
    case TASK_IDS.OPEN_SCHLANGENBÄNDIGER: return checkSchlangenbaendiger(allPlayerSnakes);
    case TASK_IDS.OPEN_SONDERKARTEN_SAMMLER: return checkSonderkartenSammler(gameStateContext?.playedSpecialCardsHistory || []);
    case TASK_IDS.OPEN_FARBHARMONIE: return checkFarbharmonie(allPlayerSnakes);
    
    case TASK_IDS.SECRET_GELBER_SCHATZ: return checkGelberSchatz(allPlayerSnakes);
    case TASK_IDS.SECRET_FUSIONSEXPERTE: return checkFusionsexperte(allPlayerSnakes);
    case TASK_IDS.SECRET_VIELFALTSKÖNIG: return checkVielfaltskoenig(allPlayerSnakes);
    case TASK_IDS.SECRET_GRÜNER_RIESE: return checkGruenerRiese(allPlayerSnakes);
    case TASK_IDS.SECRET_SCHLANGENBESCHWÖRER: return checkSchlangenbeschwoerer(allPlayerSnakes);
    case TASK_IDS.SECRET_FARBENPRACHT: return checkFarbenpracht(allPlayerSnakes);
    case TASK_IDS.SECRET_SYMMETRIEMEISTER: return checkSymmetriemeister(allPlayerSnakes);
    default: return false;
  }
};