/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{js,jsx}',
    './components/**/*.{js,jsx}',
    './app/**/*.{js,jsx}',
    './src/**/*.{js,jsx}',
    './index.html',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ['Roboto', 'Open Sans', 'sans-serif'], // Fließtext
        heading: ['Playfair Display', 'serif'], // Überschriften
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))", // Hellgrau #F5F5F5
        foreground: "hsl(var(--foreground))", // Weiß für Text auf dunklen Hintergründen
        
        primary: { // Wird als Haupt-Akzentfarbe (Türkis) verwendet, da es nur ein 'primary' gibt
          DEFAULT: "hsl(var(--primary))", // Türkis #3AC4AB
          foreground: "hsl(var(--primary-foreground))", // Weiß oder sehr dunkles Grau für Kontrast
        },
        // Spezifische Primärfarben aus dem Design-Dokument
        'primary-dark-green': "hsl(var(--primary-dark-green))", // Dunkelgrün
        'primary-burgundy': "hsl(var(--primary-burgundy))",   // Burgunderrot
        'primary-gold': "hsl(var(--primary-gold))",         // Gold

        secondary: { // Wird für Flächen verwendet, die nicht primär sind
          DEFAULT: "hsl(var(--secondary))", // z.B. ein dunklerer Ton des Hintergrunds oder ein neutrales Grau
          foreground: "hsl(var(--secondary-foreground))",
        },
        accent: { // Kann für Hover-States oder sekundäre Akzente genutzt werden
          DEFAULT: "hsl(var(--accent))", // Türkis #3AC4AB (identisch zu primary hier, kann angepasst werden)
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: { // Kartenhintergründe, können thematisch sein
          DEFAULT: "hsl(var(--card))", // z.B. Dunkelgrün als Standard-Kartenhintergrund
          foreground: "hsl(var(--card-foreground))", // Text auf Karten
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}