import React, { useEffect, useRef } from 'react';
import { debugLog, isDebugMode } from '@/utils/debugUtils.jsx';

const GameStateMonitor = ({ gameState }) => {
  const previousStateRef = useRef(null);
  const renderCountRef = useRef(0);
  const lastLogTimeRef = useRef(0);

  useEffect(() => {
    if (!isDebugMode()) return;

    renderCountRef.current++;
    const now = Date.now();
    
    // Nur alle 2 Sekunden loggen, um Spam zu vermeiden
    if (now - lastLogTimeRef.current < 2000) return;
    lastLogTimeRef.current = now;

    const currentState = {
      currentPlayerId: gameState.currentPlayerId,
      isPlayerTurn: gameState.isPlayerTurn,
      gamePhase: gameState.gamePhase,
      modalRequired: !!gameState.modalRequired,
      aiOpponentsData: gameState.aiOpponentsData?.map(ai => ({
        id: ai.id,
        handSize: ai.hand?.length || 0,
        isBlocked: ai.isBlockedByGrube,
        colorCardsPlayed: ai.colorCardsPlayedThisTurn || 0,
        specialCardsPlayed: ai.specialCardsPlayedThisTurn || 0
      })) || []
    };

    const previousState = previousStateRef.current;
    
    // Prüfe auf verdächtige Änderungen
    if (previousState) {
      const hasSignificantChange = 
        currentState.currentPlayerId !== previousState.currentPlayerId ||
        currentState.isPlayerTurn !== previousState.isPlayerTurn ||
        currentState.gamePhase !== previousState.gamePhase ||
        currentState.modalRequired !== previousState.modalRequired;

      if (hasSignificantChange) {
        debugLog(`[GameStateMonitor] Render #${renderCountRef.current} - Significant change detected:`, {
          previous: previousState,
          current: currentState
        });
      }

      // Prüfe auf potentielle Endlosschleifen
      if (renderCountRef.current > 50) {
        console.warn(`[GameStateMonitor] High render count detected: ${renderCountRef.current}. Possible infinite loop!`);
        renderCountRef.current = 0; // Reset counter
      }
    }

    previousStateRef.current = currentState;
  }, [gameState]);

  // Komponente rendert nichts sichtbares
  return null;
};

export default GameStateMonitor;
