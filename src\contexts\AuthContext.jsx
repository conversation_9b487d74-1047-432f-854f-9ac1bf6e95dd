import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { useNavigate, useLocation } from 'react-router-dom';

const AuthContext = createContext(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [signInError, setSignInError] = useState(null);
  const [signUpError, setSignUpError] = useState(null);
  const [signUpSuccess, setSignUpSuccess] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const getSession = async () => {
      setLoading(true);
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error("Error getting session:", error);
        setLoading(false);
        setIsAuthenticated(false);
        return;
      }
      setUser(session?.user ?? null);
      setIsAuthenticated(!!session?.user);
      setLoading(false);
    };
    getSession();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        const currentUser = session?.user ?? null;
        setUser(currentUser);
        setIsAuthenticated(!!currentUser);
        setLoading(false);
        
        if (_event === 'SIGNED_IN' && currentUser) {
          const from = location.state?.from?.pathname || '/main-menu';
          if (location.pathname !== from) {
            navigate(from, { replace: true });
          }
        } else if (_event === 'SIGNED_OUT') {
          if (location.pathname !== '/signin') {
            navigate('/signin');
          }
        }
      }
    );
    
    return () => {
      if (authListener && authListener.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [navigate, location.pathname, location.state]);

  const value = {
    user,
    loading,
    isAuthenticated,
    signInError,
    signUpError,
    signUpSuccess,
    setSignInError,
    setSignUpError,
    setSignUpSuccess,
    signIn: async (email, password) => {
      setLoading(true);
      setSignInError(null);
      const { error: signInError, data } = await supabase.auth.signInWithPassword({ email, password });
      setLoading(false); 
      if (signInError) {
        setSignInError(signInError.message);
        if (signInError.message.includes("Email not confirmed")) {
          setSignInError("Bitte bestätige deine E-Mail-Adresse, bevor du dich anmeldest. Überprüfe deinen Posteingang (und Spam-Ordner).");
        } else if (signInError.message.includes("Invalid login credentials")) {
          setSignInError("Ungültige Anmeldedaten. Bitte überprüfe deine E-Mail und dein Passwort.");
        }
        setIsAuthenticated(false);
      } else {
        setUser(data.user);
        setIsAuthenticated(true);
      }
      return { error: signInError, user: data?.user };
    },
    signUp: async (email, password, username) => {
      setLoading(true);
      setSignUpError(null);
      setSignUpSuccess(false);
      const { error: signUpError, data } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username,
            full_name: username, 
          },
        },
      });
      setLoading(false);
      if (signUpError) {
        setSignUpError(signUpError.message);
        setIsAuthenticated(false);
      } else {
        if (data.user && data.user.identities && data.user.identities.length === 0) {
           setSignUpError("Ein Benutzer mit dieser E-Mail-Adresse existiert bereits, ist aber möglicherweise nicht bestätigt.");
           setIsAuthenticated(false);
        } else if (data.user && data.session) { 
          setSignUpSuccess(true);
          setUser(data.user);
          setIsAuthenticated(true);
        } else if (data.user) { 
           setSignUpSuccess(true); 
           setUser(data.user); 
        }
         else {
          setSignUpError("Ein unbekannter Fehler ist aufgetreten. Bitte versuche es später erneut.");
          setIsAuthenticated(false);
        }
      }
      return { error: signUpError, user: data?.user, session: data?.session };
    },
    signOut: async () => {
      setLoading(true);
      await supabase.auth.signOut();
      setUser(null);
      setIsAuthenticated(false);
      setLoading(false);
    },
    updateUserMetadata: async (metadata) => {
        setLoading(true);
        const { data, error } = await supabase.auth.updateUser({
            data: metadata 
        });
        setLoading(false);
        if (error) {
            console.error('Error updating user metadata:', error);
            return { error };
        }
        setUser(data.user);
        return { user: data.user };
    },
    sendPasswordResetEmail: async (email) => {
        setLoading(true);
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/update-password`, 
        });
        setLoading(false);
        return { error };
    },
    updatePassword: async (newPassword) => {
        setLoading(true);
        const { error } = await supabase.auth.updateUser({ password: newPassword });
        setLoading(false);
        return { error };
    }
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};