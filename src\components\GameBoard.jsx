import React, { useEffect, useRef, useState, useCallback } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { motion } from 'framer-motion';
import { useToast } from "@/components/ui/use-toast";

import StatsPanel from '@/components/StatsPanel';
import PlayerHandDisplay from '@/components/gameboard/PlayerHandDisplay';
import GameBoardActions from '@/components/gameboard/GameBoardActions';
import GameBoardHeader from '@/components/gameboard/GameBoardHeader';
import TaskPanel from '@/components/gameboard/TaskPanel';
import GameArea from '@/components/gameboard/GameArea';
import InteractionModal from '@/components/gameboard/InteractionModal';
import GameOverModal from '@/components/gameboard/GameOverModal';
import { isDebugMode, DebugPanel, dumpGameState } from '@/utils/debugUtils.jsx';
import GameStateMonitor from '@/components/GameStateMonitor';

import {
  createDeck,
  shuffleDeck,
  dealCards,
  createPlayerAuxCards,
  CARD_TYPES,
  SPECIAL_CARDS_NAMES
} from '@/utils/gameRules';
import {
  canActivateComebackCard,
} from '@/utils/gameLogic.jsx';
import { initializeGameState, startTimer, stopTimer } from '@/utils/gameStateManager';
import { usePlayerActions } from '@/hooks/usePlayerActions';
import { useAiTurn } from '@/hooks/useAiTurn';
import { setForceFallback } from '@/utils/aiPlayerLogic';

// Debug-Komponente zur Anzeige der KI-Karten
const AiHandDebugDisplay = ({ aiOpponent }) => {
  if (!isDebugMode() || !aiOpponent) return null;

  return (
    <div className="bg-yellow-900/30 border border-yellow-600/50 p-2 rounded-lg mb-2">
      <div className="text-xs font-bold text-yellow-400 mb-2">
        🔍 DEBUG: Gegner {aiOpponent.id} Karten ({aiOpponent.hand?.length || 0})
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-1">
        {(aiOpponent.hand || []).map((card, index) => (
          <div
            key={card.id || index}
            className={`
              text-xs p-1 rounded border
              ${card.type === 'COLOR'
                ? `bg-${card.color}-800/50 border-${card.color}-500/50 text-${card.color}-200`
                : 'bg-purple-800/50 border-purple-500/50 text-purple-200'
              }
            `}
            title={`${card.name} (${card.type}, ${card.points || 0}P)`}
          >
            <div className="font-semibold">{card.name}</div>
            <div className="text-xs opacity-75">{card.points || 0}P</div>
          </div>
        ))}
      </div>
    </div>
  );
};

const GameBoard = () => {
  const { toast } = useToast();
  const timerIntervalRef = useRef(null);
  const [gameState, setGameState] = useState(initializeGameState());

  const {
    playSelectedCardLogic,
    handleCardSelectFromHand,
    handleDragStartFromHand,
    handleDropOnSnake,
    handleClickOnSnakePosition,
    handleDropOnDiscard,
    handleCompleteTask,
    endTurn,
    handleModalSubmit,
    handleStartNewSnake,
    handleActivateComeback,
    handleActivateRiskReward,
  } = usePlayerActions(gameState, setGameState);

  const { executeAiTurn, aiTurnInProgressRef } = useAiTurn(gameState, setGameState, playSelectedCardLogic);


  const startGame = useCallback(() => {
    aiTurnInProgressRef.current = false;
    const gameConfig = JSON.parse(localStorage.getItem('gameConfig') || '{}');
    const numTotalPlayers = 1 + (gameConfig.aiOpponents || 0);

    const initialDeck = createDeck();
    const shuffledDeck = shuffleDeck(initialDeck);
    const { hands, remainingDeck } = dealCards(shuffledDeck, numTotalPlayers, 5);

    setGameState(prev => {
        const freshState = initializeGameState(numTotalPlayers);
        return {
            ...freshState,
            deck: remainingDeck,
            playerHand: hands[0] || [],
            auxCards: createPlayerAuxCards(),
            aiOpponentsData: hands.slice(1).map((hand, idx) => ({
                id: idx + 1,
                name: `Gegner ${idx + 1}`,
                hand,
                snakes: [[]],
                activeSnakeIndex: 0,
                points: 0,
                isBlockedByGrube: false,
                colorCardsPlayedThisTurn: 0,
                specialCardsPlayedThisTurn: 0,
                mustPlaySpecialCard: false,
                secretTask: freshState.availableOpenTaskCards.find(t => t.type === CARD_TYPES.TASK_SECRET && t.id.includes((idx+1).toString())) || freshState.availableOpenTaskCards.pop()
            })),
            gamePhase: 'playing',
            isPlayerTurn: true,
            currentPlayerId: 0,
            selectedCardForPlay: null,
            toastInfoForDisplay: { title: "Neues Spiel gestartet!", description: "Möge die beste Schlange gewinnen!", duration: 3000 }
        };
    });

    startTimer(timerIntervalRef, setGameState);
  }, [setGameState, aiTurnInProgressRef]);

  useEffect(() => {
    startGame();
    return () => stopTimer(timerIntervalRef);
  }, [startGame]);


  useEffect(() => {
    const onlySpecial = gameState.playerHand.length > 0 && gameState.playerHand.every(card => card.type === CARD_TYPES.SPECIAL);
    const newMustPlaySpecialCard = onlySpecial && (gameState.colorCardsPlayedThisTurn || 0) === 0 && (gameState.specialCardsPlayedThisTurn || 0) === 0 && !gameState.comebackActive;

    if (newMustPlaySpecialCard !== gameState.mustPlaySpecialCard) {
      setGameState(prev => ({ ...prev, mustPlaySpecialCard: newMustPlaySpecialCard }));
    }
  }, [gameState.playerHand, gameState.colorCardsPlayedThisTurn, gameState.specialCardsPlayedThisTurn, gameState.comebackActive, gameState.mustPlaySpecialCard, setGameState]);

  useEffect(() => {
    if (gameState.toastInfoForDisplay) {
      toast(gameState.toastInfoForDisplay);
      setGameState(prev => ({ ...prev, toastInfoForDisplay: null }));
    }
  }, [gameState.toastInfoForDisplay, toast, setGameState]);

  useEffect(() => {
    if (!gameState.isPlayerTurn && gameState.currentPlayerId !== 0 && gameState.gamePhase === 'playing' && !gameState.modalRequired && !aiTurnInProgressRef.current) {
      executeAiTurn(gameState.currentPlayerId);
    }
  }, [gameState.isPlayerTurn, gameState.currentPlayerId, gameState.gamePhase, gameState.modalRequired, executeAiTurn, aiTurnInProgressRef]);


  const isVerdopplerActiveCurrentTurn = gameState.playedSpecialCardsHistory.some(
    (c) => c.name === SPECIAL_CARDS_NAMES.DOUBLER && c.turnPlayed === gameState.turnNumber && c.playerId === 0
  );

  const canEndTurn =
    gameState.isPlayerTurn &&
    (
      ((gameState.colorCardsPlayedThisTurn || 0) > 0 || (gameState.specialCardsPlayedThisTurn || 0) > 0) ||
      (gameState.schlangenkorbPlayState.active && gameState.schlangenkorbPlayState.playedCount >= gameState.schlangenkorbPlayState.cardsToPlay) ||
      (gameState.comebackActive && gameState.comebackActionsRemaining === 0) ||
      gameState.isBlockedByGrube // SCHLANGENGRUBE: Blockierte Spieler dürfen Zug beenden ohne Karten zu spielen
    ) &&
    !gameState.modalRequired;

  // DEBUG: Log blocking state changes
  React.useEffect(() => {
    if (gameState.isPlayerTurn) {
      debugLog(`[GameBoard] PLAYER TURN: isBlockedByGrube=${gameState.isBlockedByGrube}, canEndTurn=${canEndTurn}, cardsPlayed=${(gameState.colorCardsPlayedThisTurn || 0) + (gameState.specialCardsPlayedThisTurn || 0)}`);
    }
  }, [gameState.isPlayerTurn, gameState.isBlockedByGrube, canEndTurn, gameState.colorCardsPlayedThisTurn, gameState.specialCardsPlayedThisTurn]);

  // Debug Quick Actions Handler
  const handleDebugAction = (action, params = {}) => {
    switch (action) {
      case 'give_ai_special_card':
        // Neue universelle Funktion für alle Sonderkarten
        if (gameState.aiOpponentsData.length > 0 && params.cardName) {
          const specialCard = {
            id: `debug-${params.cardName.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
            name: params.cardName,
            type: CARD_TYPES.SPECIAL,
            points: 0,
            color: null,
            description: `Debug: ${params.cardName} für KI-Test`
          };

          setGameState(prev => ({
            ...prev,
            aiOpponentsData: prev.aiOpponentsData.map((opp, idx) =>
              idx === 0 ? { ...opp, hand: [...opp.hand, specialCard] } : opp
            )
          }));

          // Automatisch Fallback-Logik aktivieren für 25 Sekunden
          console.log(`[DEBUG] 🎴 Adding ${params.cardName} to AI and activating fallback mode...`);
          setForceFallback(true);
          setTimeout(() => {
            console.log(`[DEBUG] 🎴 Deactivating fallback mode after ${params.cardName} test...`);
            setForceFallback(false);
          }, 25000);
        }
        break;
      case 'give_ai_verdoppler':
        // Füge KI einen Verdoppler zur Hand hinzu (nur für Testing)
        if (gameState.aiOpponentsData.length > 0) {
          const verdopplerCard = {
            id: `debug-verdoppler-${Date.now()}`,
            name: SPECIAL_CARDS_NAMES.DOUBLER,
            type: CARD_TYPES.SPECIAL,
            points: 0,
            color: null,
            description: 'Debug: Verdoppler für KI'
          };
          setGameState(prev => ({
            ...prev,
            aiOpponentsData: prev.aiOpponentsData.map((opp, idx) =>
              idx === 0 ? { ...opp, hand: [...opp.hand, verdopplerCard] } : opp
            )
          }));

          // Automatisch Fallback-Logik aktivieren für 20 Sekunden
          console.log('[DEBUG] Activating fallback mode for Verdoppler testing...');
          setForceFallback(true);
          setTimeout(() => {
            console.log('[DEBUG] Deactivating fallback mode after 20 seconds...');
            setForceFallback(false);
          }, 20000);
        }
        break;
      case 'block_ai':
        // Blockiere erste KI
        if (gameState.aiOpponentsData.length > 0) {
          setGameState(prev => ({
            ...prev,
            aiOpponentsData: prev.aiOpponentsData.map((opp, idx) =>
              idx === 0 ? { ...opp, isBlockedByGrube: true } : opp
            )
          }));
        }
        break;
      case 'block_human':
        // DEBUG: Blockiere menschlichen Spieler manuell
        setGameState(prev => ({
          ...prev,
          isBlockedByGrube: true,
          aiTurnsWhilePlayerBlocked: 0
        }));
        console.log('[DEBUG] 🕳️ Human player manually blocked for testing');
        break;
      case 'force_fallback':
        // Erzwinge Fallback-Logik für die nächsten KI-Züge
        setForceFallback(true);
        setTimeout(() => {
          setForceFallback(false);
        }, 30000); // Nach 30 Sekunden wieder deaktivieren
        break;
      case 'dump_state':
        dumpGameState(gameState, 'Manual Debug Dump');
        break;
      default:
        console.log('Unknown debug action:', action);
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="min-h-screen flex flex-col items-center justify-start bg-gradient-to-br from-slate-900 via-primary-dark-green to-slate-800 text-slate-100 p-1 sm:p-2 md:p-4 relative overflow-hidden">

        <GameBoardHeader onStartGame={startGame} onEndTurn={endTurn} gameState={gameState} canEndTurn={canEndTurn} />

        <motion.div
          className="w-full my-4 sm:my-6 md:my-8"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <StatsPanel gameState={gameState} />
        </motion.div>

        <GameBoardActions
            mustPlaySpecialCard={gameState.mustPlaySpecialCard}
            canActivateComeback={canActivateComebackCard(gameState)}
            onActivateComeback={handleActivateComeback}
            comebackCardUsed={gameState.auxCards.comebackCard.used}
            riskRewardCardsRemaining={gameState.auxCards.riskRewardCards.filter(c => !c.used).length}
            onActivateRiskReward={handleActivateRiskReward}
        />

        <div className="flex flex-col md:flex-row gap-2 sm:gap-4 w-full px-1 sm:px-2 md:px-4 flex-grow">
          <TaskPanel
            openTasks={gameState.openTasks}
            secretTask={gameState.secretTask}
            playerTasks={gameState.playerTasks}
            auxCards={gameState.auxCards}
            onCompleteTask={handleCompleteTask}
            activeSnake={gameState.playerSnakes[gameState.activeSnakeIndex]}
            className="md:w-1/4"
          />
          <GameArea
            playerSnakes={gameState.playerSnakes}
            aiOpponentsData={gameState.aiOpponentsData}
            activeSnakeIndex={gameState.activeSnakeIndex}
            isPlayerTurn={gameState.isPlayerTurn}
            currentPlayerId={gameState.currentPlayerId}
            selectedCardForPlay={gameState.selectedCardForPlay}
            discardPileLength={gameState.discardPile.length}
            deckLength={gameState.deck.length}
            onDropOnSnake={handleDropOnSnake}
            onClickOnSnakePosition={handleClickOnSnakePosition}
            onDropOnDiscard={handleDropOnDiscard}
            onStartNewSnake={handleStartNewSnake}
            onSetActiveSnakeIndex={(index) => {
                setGameState(prev => ({...prev, activeSnakeIndex: index}))
            }}
            gamePhase={gameState.gamePhase}
            className="md:w-3/4"
          />
        </div>

        {(gameState.gamePhase === 'playing' || gameState.gamePhase === 'lastRound') && (
          <motion.div
            className="mt-auto w-full max-w-5xl p-1 sm:p-2 md:p-3 bg-slate-800/80 backdrop-blur-md rounded-t-lg shadow-inner fixed bottom-0 left-0 right-0 z-10"
            initial={{ y: 150, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6, type: "spring", stiffness:100 }}
          >
            <h3 className="text-center text-slate-300 text-sm sm:text-base mb-1 font-semibold font-heading">
              {gameState.isPlayerTurn ? "Dein Zug" : `Gegner ${gameState.currentPlayerId} ist am Zug...`}
              {gameState.gamePhase === 'lastRound' && <span className="text-red-400 font-bold"> (LETZTE RUNDE)</span>}
              {gameState.isPlayerTurn && gameState.isBlockedByGrube && <span className="text-orange-400 font-bold"> 🕳️ (BLOCKIERT)</span>}
              {gameState.isPlayerTurn && !gameState.isBlockedByGrube &&
                ` (Gespielt: ${gameState.colorCardsPlayedThisTurn || 0} Farbe, ${gameState.specialCardsPlayedThisTurn || 0} Spezial / Max:
                ${gameState.comebackActive ? gameState.comebackActionsRemaining : (gameState.schlangenkorbPlayState.active ? gameState.schlangenkorbPlayState.cardsToPlay - gameState.schlangenkorbPlayState.playedCount : (isVerdopplerActiveCurrentTurn ? '3 (flexibel)' : '1 Farbe & 1 Spezial'))})`
              }
              {gameState.isPlayerTurn && gameState.schlangenkorbPlayState.active && ` | Korb: ${gameState.schlangenkorbPlayState.playedCount}/${gameState.schlangenkorbPlayState.cardsToPlay}`}
              {gameState.isPlayerTurn && gameState.comebackActive && ` | Comeback Aktionen: ${gameState.comebackActionsRemaining}`}
            </h3>
            {gameState.isPlayerTurn && (
              <PlayerHandDisplay
                cards={gameState.playerHand}
                onCardSelect={handleCardSelectFromHand}
                selectedCardId={gameState.selectedCardForPlay?.id}
                onDragStart={handleDragStartFromHand}
              />
            )}
          </motion.div>
        )}

        {gameState.modalRequired && gameState.isPlayerTurn && (
            <InteractionModal
                isOpen={!!gameState.modalRequired}
                onClose={() => setGameState(prev => ({...prev, modalRequired: null}))}
                modalType={gameState.modalRequired.type}
                modalData={gameState.modalRequired}
                gameState={gameState}
                onSubmit={handleModalSubmit}
            />
        )}
        {gameState.gamePhase === 'gameOver' && (
            <GameOverModal
                isOpen={true}
                scores={gameState.playerFinalScores}
                onPlayAgain={startGame}
            />
        )}

        {/* Debug Panel */}
        {isDebugMode() && (
          <DebugPanel gameState={gameState} onAction={handleDebugAction} />
        )}

        {/* DEBUG: KI-Karten Anzeige */}
        {isDebugMode() && gameState.aiOpponentsData && (
          <div className="mb-4">
            {gameState.aiOpponentsData.map(opponent => (
              <AiHandDebugDisplay key={opponent.id} aiOpponent={opponent} />
            ))}
          </div>
        )}

        {/* Game State Monitor für Debugging */}
        <GameStateMonitor gameState={gameState} />
      </div>
    </DndProvider>
  );
};

export default GameBoard;
